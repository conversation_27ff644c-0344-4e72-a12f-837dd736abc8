* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    overflow-x: hidden;
    width: 100%;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: rgb(11, 8, 16);
    color: rgba(255, 255, 255, 0.9);
    overflow-x: hidden;
    width: 100%;
    position: relative;
}

.dashboard-container {
    display: flex;
    min-height: 100vh;
    width: 100%;
    overflow-x: hidden;
}

/* Sidebar Styles */
.sidebar {
    width: 250px;
    min-width: 250px;
    background: linear-gradient(135deg, #1a7de8 0%, #1565C0 100%);
    color: white;
    padding: 20px 0;
    box-shadow: 2px 0 10px rgba(0,0,0,0.3);
    position: relative;
    z-index: 10;
}

.sidebar-header {
    padding: 0 20px 30px;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.sidebar-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
}

.nav-menu {
    list-style: none;
    padding: 20px 0;
}

.nav-item {
    margin: 5px 0;
}

.nav-item a {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: all 0.3s ease;
}

.nav-item a:hover,
.nav-item.active a {
    background-color: rgba(255,255,255,0.1);
    color: white;
    border-right: 3px solid #fff;
}

.nav-item i {
    margin-right: 10px;
    width: 20px;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
    overflow-x: hidden;
    background-color: rgb(11, 8, 16);
    width: calc(100% - 250px);
    max-width: 100%;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.section-header h1 {
    color: #fff;
    font-size: 2rem;
    font-weight: 600;
    text-shadow: 0 0 10px rgba(26, 125, 232, 0.4);
}

.search-bar {
    position: relative;
    flex: 1;
    max-width: 400px;
}

.search-bar input {
    width: 100%;
    padding: 12px 40px 12px 15px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    background-color: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.9);
    border-radius: 25px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.search-bar input:focus {
    outline: none;
    border-color: #1a7de8;
}

.search-bar i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.7);
}

/* Unified Filter Menu Styles */
.unified-filter-menu {
    margin-bottom: 30px;
    width: 100%;
}

.filter-container {
    background: linear-gradient(145deg, rgba(30, 25, 40, 0.95), rgba(25, 20, 35, 0.95));
    border-radius: 16px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

.main-filter-title {
    color: rgba(255, 255, 255, 0.95);
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 24px;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
    gap: 12px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.main-filter-title i {
    color: #1a7de8;
    font-size: 1.2rem;
}

.filter-groups {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.filter-group {
    background: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    padding: 18px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
}

.filter-group:hover {
    background: rgba(255, 255, 255, 0.04);
    border-color: rgba(255, 255, 255, 0.1);
}

.filter-group-title {
    color: rgba(255, 255, 255, 0.85);
    font-size: 0.95rem;
    font-weight: 600;
    margin-bottom: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
}

.filter-group-title::before {
    content: '';
    width: 3px;
    height: 14px;
    background: linear-gradient(135deg, #1a7de8, #1565C0);
    border-radius: 2px;
}

.filter-tabs {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    background: rgba(255, 255, 255, 0.03);
    padding: 8px;
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.filter-tab {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 10px 16px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0.85rem;
    font-weight: 500;
    min-width: 120px;
    justify-content: center;
    position: relative;
    flex: 1;
}

.filter-tab:hover {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.approval-tab.active {
    background: linear-gradient(135deg, #1a7de8 0%, #1565C0 100%);
    color: white;
    border-color: #1a7de8;
    box-shadow: 0 4px 15px rgba(26, 125, 232, 0.3);
}

.activation-tab.active {
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
    color: white;
    border-color: #27ae60;
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.approval-tab.active:hover {
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(26, 125, 232, 0.4);
}

.activation-tab.active:hover {
    background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
}

.filter-tab i {
    font-size: 0.9rem;
    flex-shrink: 0;
}

.filter-tab span {
    white-space: nowrap;
}

.tab-count {
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.9);
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 600;
    min-width: 18px;
    text-align: center;
    margin-left: 4px;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

.filter-tab.active .tab-count {
    background: rgba(255, 255, 255, 0.25);
    color: white;
    transform: scale(1.05);
}

.filter-tab:hover .tab-count {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.02);
}

/* Dropdown Filter Styling */
.dropdown-filter-group {
    background: linear-gradient(135deg, rgba(26, 125, 232, 0.1), rgba(21, 101, 192, 0.1));
    border: 1px solid rgba(26, 125, 232, 0.2);
}

.dropdown-filter-container {
    position: relative;
    display: flex;
    align-items: center;
}

.filter-dropdown {
    width: 100%;
    padding: 12px 40px 12px 16px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.04));
    border: 2px solid rgba(26, 125, 232, 0.3);
    border-radius: 10px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    appearance: none;
    backdrop-filter: blur(10px);
}

.filter-dropdown:hover {
    border-color: rgba(26, 125, 232, 0.5);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.06));
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(26, 125, 232, 0.2);
}

.filter-dropdown:focus {
    outline: none;
    border-color: #1a7de8;
    box-shadow: 0 0 0 3px rgba(26, 125, 232, 0.2);
}

.filter-dropdown option {
    background: rgb(30, 25, 40);
    color: white;
    padding: 10px;
    font-weight: 500;
}

.filter-dropdown option:hover {
    background: rgba(26, 125, 232, 0.2);
}

.dropdown-icon {
    position: absolute;
    right: 12px;
    color: rgba(26, 125, 232, 0.7);
    font-size: 0.8rem;
    pointer-events: none;
    transition: all 0.3s ease;
}

.dropdown-filter-container:hover .dropdown-icon {
    color: #1a7de8;
    transform: translateY(-1px);
}

.filter-dropdown:focus + .dropdown-icon {
    transform: rotate(180deg);
    color: #1a7de8;
}

/* Advanced Filters Styling */
.advanced-filters {
    background: linear-gradient(135deg, rgba(155, 89, 182, 0.1), rgba(142, 68, 173, 0.1));
    border: 1px solid rgba(155, 89, 182, 0.2);
}

.advanced-filter-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.filter-control-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-label {
    color: rgba(255, 255, 255, 0.85);
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.advanced-filter-select {
    padding: 10px 14px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.04));
    border: 2px solid rgba(155, 89, 182, 0.3);
    border-radius: 8px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
}

.advanced-filter-select:hover {
    border-color: rgba(155, 89, 182, 0.5);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.06));
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(155, 89, 182, 0.2);
}

.advanced-filter-select:focus {
    outline: none;
    border-color: #9b59b6;
    box-shadow: 0 0 0 3px rgba(155, 89, 182, 0.2);
}

.advanced-filter-select option {
    background: rgb(30, 25, 40);
    color: white;
    padding: 8px;
    font-weight: 500;
}

.clear-advanced-filters-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.1));
    border: 2px solid rgba(231, 76, 60, 0.3);
    border-radius: 8px;
    color: rgba(231, 76, 60, 0.9);
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
}

.clear-advanced-filters-btn:hover {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.2), rgba(192, 57, 43, 0.2));
    border-color: rgba(231, 76, 60, 0.5);
    color: #e74c3c;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.2);
}

.clear-advanced-filters-btn i {
    font-size: 0.8rem;
}

/* Enhanced Bulk Operations Styling */
.bulk-group {
    display: flex;
    gap: 8px;
    align-items: center;
    padding: 0 12px;
    border-left: 1px solid rgba(255, 255, 255, 0.1);
}

.bulk-group:first-of-type {
    border-left: none;
    padding-left: 0;
}

.bulk-btn.activate-btn {
    border-color: rgba(39, 174, 96, 0.5);
    color: #27ae60;
}

.bulk-btn.activate-btn:hover {
    background-color: rgba(39, 174, 96, 0.2);
    border-color: #27ae60;
    box-shadow: 0 0 10px rgba(39, 174, 96, 0.5);
}

.bulk-btn.deactivate-btn {
    border-color: rgba(230, 126, 34, 0.5);
    color: #e67e22;
}

.bulk-btn.deactivate-btn:hover {
    background-color: rgba(230, 126, 34, 0.2);
    border-color: #e67e22;
    box-shadow: 0 0 10px rgba(230, 126, 34, 0.5);
}

/* Responsive design for unified filter */
@media (max-width: 1024px) {
    .filter-groups {
        gap: 16px;
    }

    .filter-group {
        padding: 15px;
    }

    .filter-tabs {
        gap: 6px;
    }

    .filter-tab {
        min-width: 100px;
        padding: 8px 12px;
        font-size: 0.8rem;
    }
}

@media (max-width: 768px) {
    .unified-filter-menu {
        margin-bottom: 20px;
    }

    .filter-container {
        padding: 18px;
        border-radius: 12px;
    }

    .main-filter-title {
        font-size: 1.1rem;
        margin-bottom: 18px;
    }

    .filter-groups {
        gap: 12px;
    }

    .filter-group {
        padding: 12px;
    }

    .filter-group-title {
        font-size: 0.85rem;
        margin-bottom: 10px;
    }

    .filter-tabs {
        flex-direction: column;
        gap: 6px;
        padding: 6px;
    }

    .filter-tab {
        min-width: auto;
        width: 100%;
        padding: 10px 12px;
        font-size: 0.8rem;
    }

    .filter-dropdown {
        padding: 10px 35px 10px 12px;
        font-size: 0.85rem;
    }

    .dropdown-icon {
        right: 10px;
        font-size: 0.75rem;
    }

    .advanced-filter-controls {
        gap: 12px;
    }

    .advanced-filter-select {
        padding: 8px 12px;
        font-size: 0.8rem;
    }

    .clear-advanced-filters-btn {
        padding: 8px 14px;
        font-size: 0.8rem;
    }

    .bulk-group {
        flex-direction: column;
        gap: 6px;
        border-left: none;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        padding: 8px 0 0 0;
    }

    .bulk-group:first-of-type {
        border-top: none;
        padding-top: 0;
    }
}

@media (max-width: 480px) {
    .filter-container {
        padding: 15px;
    }

    .main-filter-title {
        font-size: 1rem;
        margin-bottom: 15px;
    }

    .filter-tab {
        padding: 8px 10px;
        font-size: 0.75rem;
    }

    .tab-count {
        font-size: 0.7rem;
        padding: 1px 6px;
    }
}

/* Responsive design for user cards */
@media (max-width: 768px) {
    .users-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .user-profile-section {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 12px;
    }

    .user-basic-info {
        width: 100%;
    }

    .user-meta {
        align-items: center;
    }

    .user-quick-actions {
        flex-wrap: wrap;
        gap: 6px;
    }

    .quick-action-btn {
        flex: 1;
        min-width: 60px;
        font-size: 0.75rem;
        padding: 6px 8px;
    }

    .quick-action-btn span {
        display: none;
    }

    .quick-action-btn i {
        margin: 0;
    }
}

@media (max-width: 480px) {
    .user-avatar img {
        width: 48px;
        height: 48px;
    }

    .user-level-badge {
        width: 20px;
        height: 20px;
        font-size: 0.7rem;
    }

    .progress-item {
        padding: 8px;
        gap: 8px;
    }

    .progress-icon {
        width: 32px;
        height: 32px;
        font-size: 0.9rem;
    }
}

/* Users Grid */
.users-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
    width: 100%;
    max-width: 100%;
}

/* Enhanced User Card Styles */
.user-card {
    background: linear-gradient(145deg, rgba(30, 25, 40, 0.95), rgba(25, 20, 35, 0.95));
    border-radius: 16px;
    padding: 0;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    width: 100%;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.user-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 16px 48px rgba(26, 125, 232, 0.25);
    border-color: rgba(26, 125, 232, 0.3);
}

.user-card.selected {
    border-color: #1a7de8;
    box-shadow: 0 12px 40px rgba(26, 125, 232, 0.4);
    background: linear-gradient(145deg, rgba(26, 125, 232, 0.15), rgba(21, 101, 192, 0.15));
}

.user-card.selected:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 56px rgba(26, 125, 232, 0.5);
}

.banned-card {
    border-color: #e74c3c;
    box-shadow: 0 8px 32px rgba(231, 76, 60, 0.2);
    background: linear-gradient(145deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.1));
}

/* Status badges positioned in user meta section */
.user-status-info {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
}

.status-badge {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 10px;
    border-radius: 16px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.status-badge i {
    font-size: 0.7rem;
}

.status-badge:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

/* Enhanced Badge Styles */
.deactivated-badge {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

/* Updated badge styles for new container */
.ban-badge,
.pending-badge,
.approved-badge,
.rejected-badge {
    background: linear-gradient(135deg, var(--badge-color-1), var(--badge-color-2));
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    gap: 4px;
    white-space: nowrap;
}

.pending-badge {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

.approved-badge {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}

.rejected-badge {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.ban-badge {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

/* User Profile Section */
.user-profile-section {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.02), rgba(255, 255, 255, 0.01));
}

.user-avatar {
    position: relative;
    flex-shrink: 0;
}

.user-avatar img {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid rgba(26, 125, 232, 0.3);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.user-avatar:hover img {
    border-color: rgba(26, 125, 232, 0.6);
    transform: scale(1.05);
}

.user-level-badge {
    position: absolute;
    bottom: -4px;
    right: -4px;
    background: linear-gradient(135deg, #1a7de8, #1565C0);
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
    border: 2px solid rgba(30, 25, 40, 0.95);
    box-shadow: 0 2px 8px rgba(26, 125, 232, 0.4);
}

.user-basic-info {
    flex: 1;
    min-width: 0;
}

.user-name {
    color: rgba(255, 255, 255, 0.95);
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0 0 4px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.user-email {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin: 0 0 12px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-meta {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.user-meta span {
    display: flex;
    align-items: center;
    gap: 6px;
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
}

.user-meta i {
    width: 12px;
    font-size: 0.75rem;
    color: rgba(26, 125, 232, 0.8);
}

/* Enhanced Progress Stats */
.user-progress-stats {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.01), rgba(255, 255, 255, 0.02));
}

.progress-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
}

.progress-item:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(26, 125, 232, 0.3);
    transform: translateX(4px);
}

.progress-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.achievements-icon {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.levels-icon {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.experience-icon {
    background: linear-gradient(135deg, #1a7de8, #1565C0);
}

.progress-info {
    flex: 1;
    min-width: 0;
}

.progress-value {
    color: rgba(255, 255, 255, 0.95);
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 4px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.progress-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.8rem;
    margin-bottom: 8px;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, rgba(26, 125, 232, 0.8), rgba(26, 125, 232, 1));
    border-radius: 3px;
    transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 0 8px rgba(26, 125, 232, 0.5);
}

.experience-fill {
    background: linear-gradient(90deg, #1a7de8, #2196F3, #03DAC6);
    box-shadow: 0 0 12px rgba(26, 125, 232, 0.6);
}

.experience-item .progress-fill {
    background: linear-gradient(90deg, #1a7de8, #2196F3);
}

.progress-item:nth-child(1) .progress-fill {
    background: linear-gradient(90deg, #f39c12, #e67e22);
    box-shadow: 0 0 8px rgba(243, 156, 18, 0.5);
}

.progress-item:nth-child(2) .progress-fill {
    background: linear-gradient(90deg, #9b59b6, #8e44ad);
    box-shadow: 0 0 8px rgba(155, 89, 182, 0.5);
}

/* Quick Action Buttons */
.user-quick-actions {
    display: flex;
    gap: 8px;
    padding: 16px 20px 20px;
    flex-wrap: wrap;
    justify-content: center;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.02), rgba(255, 255, 255, 0.01));
}

.quick-action-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    text-decoration: none;
    min-width: 70px;
    justify-content: center;
}

.quick-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.1);
}

.quick-action-btn i {
    font-size: 0.75rem;
}

/* Specific button styles */
.view-btn {
    border-color: rgba(52, 152, 219, 0.5);
    color: #3498db;
}

.view-btn:hover {
    background: rgba(52, 152, 219, 0.2);
    border-color: #3498db;
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.approve-btn {
    border-color: rgba(39, 174, 96, 0.5);
    color: #27ae60;
}

.approve-btn:hover {
    background: rgba(39, 174, 96, 0.2);
    border-color: #27ae60;
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

.reject-btn {
    border-color: rgba(231, 76, 60, 0.5);
    color: #e74c3c;
}

.reject-btn:hover {
    background: rgba(231, 76, 60, 0.2);
    border-color: #e74c3c;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.edit-btn {
    border-color: rgba(243, 156, 18, 0.5);
    color: #f39c12;
}

.edit-btn:hover {
    background: rgba(243, 156, 18, 0.2);
    border-color: #f39c12;
    box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
}

.activate-btn {
    border-color: rgba(39, 174, 96, 0.5);
    color: #27ae60;
}

.activate-btn:hover {
    background: rgba(39, 174, 96, 0.2);
    border-color: #27ae60;
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

.deactivate-btn {
    border-color: rgba(230, 126, 34, 0.5);
    color: #e67e22;
}

.deactivate-btn:hover {
    background: rgba(230, 126, 34, 0.2);
    border-color: #e67e22;
    box-shadow: 0 4px 12px rgba(230, 126, 34, 0.3);
}

.delete-btn {
    border-color: rgba(231, 76, 60, 0.5);
    color: #e74c3c;
}

.delete-btn:hover {
    background: rgba(231, 76, 60, 0.2);
    border-color: #e74c3c;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

/* Modal Button Consistency */
.modal-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: 2px solid;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    min-width: 140px;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.modal-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.modal-btn.approve-btn {
    border-color: rgba(39, 174, 96, 0.6);
    color: #27ae60;
}

.modal-btn.approve-btn:hover {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
    border-color: #27ae60;
    box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
}

.modal-btn.cancel-btn {
    border-color: rgba(149, 165, 166, 0.6);
    color: #95a5a6;
}

.modal-btn.cancel-btn:hover {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
    border-color: #95a5a6;
    box-shadow: 0 6px 20px rgba(149, 165, 166, 0.4);
}

.modal-btn.save-btn {
    border-color: rgba(52, 152, 219, 0.6);
    color: #3498db;
}

.modal-btn.save-btn:hover {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border-color: #3498db;
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.modal-btn i {
    font-size: 0.85rem;
}

.user-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
    width: 100%;
}

.user-info {
    max-width: 75%;
    overflow: hidden;
}

.user-info h3 {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.2rem;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-info p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-actions {
    position: relative;
}

.three-dots {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #7f8c8d;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.three-dots:hover {
    background-color: #f8f9fa;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: rgb(30, 25, 40);
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    padding: 8px 0;
    min-width: 120px;
    z-index: 1000;
    display: none;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.dropdown-menu.show {
    display: block;
}

.dropdown-item {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.dropdown-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.dropdown-item i {
    margin-right: 8px;
    width: 16px;
}

.user-stats {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    width: 100%;
    overflow: hidden;
}

.stat {
    text-align: center;
    position: relative;
}

.stat-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1a7de8;
}

.stat-label {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 2px;
}

/* Experience Level Styling */
.exp-stat {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.exp-level-indicator {
    position: relative;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #1a7de8 0%, #1565C0 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 5px;
    box-shadow: 0 0 10px rgba(26, 125, 232, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.exp-level-text {
    color: white;
    font-weight: bold;
    font-size: 0.9rem;
}

.exp-bar-container {
    width: 100%;
    height: 6px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    margin-top: 8px;
    overflow: hidden;
}

.exp-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #1a7de8, #64B5F6);
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* User Modal Experience Display */
.exp-display {
    background: rgba(30, 25, 40, 0.8);
    border-radius: 10px;
    padding: 20px;
    margin-top: 15px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    position: relative;
}

.exp-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.exp-level {
    display: flex;
    align-items: center;
    gap: 10px;
}

.exp-level-badge {
    background: linear-gradient(135deg, #1a7de8 0%, #1565C0 100%);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    font-size: 1.2rem;
    box-shadow: 0 0 15px rgba(26, 125, 232, 0.5);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.exp-title {
    font-size: 1.2rem;
    color: white;
}

.max-level-tag {
    display: inline-block;
    background-color: #FFD700;
    color: #000;
    font-size: 0.8rem;
    padding: 2px 6px;
    border-radius: 4px;
    margin-left: 8px;
    font-weight: bold;
    vertical-align: middle;
}

.exp-value {
    color: #64B5F6;
    font-size: 1.1rem;
    font-weight: 500;
}

.exp-progress-container {
    height: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    margin: 15px 0;
    overflow: hidden;
    position: relative;
}

.exp-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #1a7de8, #64B5F6);
    border-radius: 5px;
    transition: width 0.3s ease;
}

.exp-progress-info {
    display: flex;
    justify-content: space-between;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.exp-next-level {
    color: #64B5F6;
}

.exp-max-level {
    color: #FFD700;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 8px;
}

.exp-max-level i {
    font-size: 1.1rem;
    animation: pulse-gold 1.5s infinite;
}

@keyframes pulse-gold {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Tooltip for experience info */
.exp-tooltip {
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(30, 25, 40, 0.9);
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.8rem;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    white-space: nowrap;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    z-index: 100;
}

.stat:hover .exp-tooltip {
    opacity: 1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .exp-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .exp-level-badge {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .exp-title {
        font-size: 1rem;
    }
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 30px;
}

.pagination button {
    background: #1a7de8;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.pagination button:hover:not(:disabled) {
    background: #1565C0;
}

.pagination button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

#pageInfo {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
}

/* Buttons */
.btn-primary {
    background: #1a7de8;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 15px rgba(26, 125, 232, 0.3);
}

.btn-primary:hover {
    background: #1565C0;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(26, 125, 232, 0.4);
}

.btn-primary:disabled {
    background: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
    opacity: 0.6;
    transform: none;
    box-shadow: none;
}

.btn-primary:disabled:hover {
    background: #6c757d;
    border-color: #6c757d;
    transform: none;
    box-shadow: none;
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-danger {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.3s ease;
}

.btn-danger:hover {
    background: #c82333;
}

/* Content Controls */
.content-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    flex-wrap: wrap;
    flex: 1;
}

.content-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.info-text {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    font-style: italic;
}

.level-controls {
    display: flex;
    gap: 10px;
}

/* Levels Container */
.levels-container {
    display: grid;
    gap: 25px;
}

/* Empty Levels State */
.empty-levels-state {
    text-align: center;
    padding: 60px 20px;
    background: rgba(30, 25, 40, 0.8);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.empty-levels-state .empty-icon {
    font-size: 4rem;
    color: rgba(26, 125, 232, 0.6);
    margin-bottom: 20px;
}

.empty-levels-state h3 {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.empty-levels-state p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1rem;
    margin-bottom: 30px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

/* Level Card Styles */
.level-card {
    background: rgba(30, 25, 40, 0.8);
    border-radius: 12px;
    padding: 0;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.05);
    overflow: hidden;
}

.level-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(26, 125, 232, 0.15);
}

.level-header {
    background: linear-gradient(135deg, rgba(26, 125, 232, 0.2), rgba(15, 101, 192, 0.2));
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.level-header-actions {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.level-header:hover {
    background: linear-gradient(135deg, rgba(26, 125, 232, 0.25), rgba(15, 101, 192, 0.25));
}

.level-header::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 60%;
    background: linear-gradient(135deg, #1a7de8, #1565C0);
    border-radius: 0 2px 2px 0;
    opacity: 0.7;
}

.level-card {
    position: relative;
}

.level-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex: 1;
}

.level-title-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
}

.level-info h3 {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.3rem;
    margin: 0;
    font-weight: 600;
    flex: 1;
}

.level-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.dropdown-toggle {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: rgba(255, 255, 255, 0.8);
    padding: 8px 10px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    min-width: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dropdown-toggle:hover {
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 1);
    transform: scale(1.05);
}

.dropdown-toggle i {
    transition: transform 0.3s ease;
}

.level-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    display: inline-block;
}

.level-status.active {
    background: #d4edda;
    color: #155724;
}

.level-status.inactive {
    background: #f8d7da;
    color: #721c24;
}

.questions-count {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.level-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn-small {
    padding: 6px 12px;
    font-size: 0.8rem;
}

.btn-small:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: #ccc !important;
    border-color: #ccc !important;
}

.btn-small:disabled:hover {
    background-color: #ccc !important;
    border-color: #ccc !important;
    transform: none;
}

/* Edit Disabled Styles */
.edit-disabled-indicator {
    color: #ff6b6b;
    margin-left: 8px;
    font-size: 0.9em;
}

.edit-disabled-text {
    color: #ff6b6b;
    font-size: 0.8rem;
    font-weight: 500;
    background: rgba(255, 107, 107, 0.1);
    padding: 2px 8px;
    border-radius: 12px;
    margin-left: 10px;
}

.limit-reached-text {
    color: #ff6b35;
    font-size: 0.8rem;
    font-weight: bold;
    background-color: rgba(255, 107, 53, 0.1);
    padding: 2px 8px;
    border-radius: 12px;
    margin-left: 10px;
    border: 1px solid rgba(255, 107, 53, 0.3);
}

.level-header[title*="disabled"] {
    opacity: 0.8;
}

.level-header[title*="disabled"]:hover {
    background: linear-gradient(135deg, rgba(26, 125, 232, 0.15), rgba(15, 101, 192, 0.15)) !important;
}

/* Pending Changes Styles */
.has-pending-changes {
    border-left: 4px solid #ffc107;
    background: rgba(255, 193, 7, 0.05);
}

.pending-indicator {
    color: #ffc107;
    font-size: 8px;
    margin-left: 8px;
    animation: pulse 2s infinite;
}

.pending-changes-text {
    color: #ffc107;
    font-size: 0.8rem;
    font-weight: 500;
    font-style: italic;
}

.pending-changes-count {
    background: #ffc107;
    color: #000;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.level-pending-indicator {
    color: #ffc107;
    font-size: 0.9rem;
    margin-left: 8px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
    transform: translateY(-1px);
}

.btn-toggle {
    padding: 8px 15px;
    font-size: 0.85rem;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
    border-radius: 8px;
    padding: 10px 16px;
}

.btn-warning:hover {
    background: #e0a800;
}

.level-questions {
    overflow-y: auto;
    transition: max-height 0.5s ease-in-out, padding 0.5s ease-in-out;
    padding: 0 20px;
}

.level-questions.collapsed {
    max-height: 0;
    padding: 0 20px;
    opacity: 0;
    transform: scaleY(0);
}

.level-questions.expanded {
    max-height: none; /* Or a larger value */
    padding: 20px;
    opacity: 1;
    transform: scaleY(1);
}

.level-questions.empty {
    text-align: center;
}

.level-questions.empty.collapsed {
    padding: 0 20px;
}

.level-questions.empty.expanded {
    padding: 40px 20px;
}

.empty-questions {
    color: rgba(255, 255, 255, 0.6);
    font-style: italic;
    font-size: 1rem;
}

.question-item {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
}

.question-item:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(26, 125, 232, 0.3);
}

.question-item:last-child {
    margin-bottom: 0;
}

.question-item .question-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.question-item .question-text {
    font-size: 1rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    flex: 1;
    margin-right: 15px;
    line-height: 1.4;
}

.question-item .question-actions {
    display: flex;
    gap: 8px;
}

.question-item .question-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.7);
}

.difficulty-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.difficulty-easy {
    background: #d4edda;
    color: #155724;
}

.difficulty-medium {
    background: #fff3cd;
    color: #856404;
}

.difficulty-hard {
    background: #f8d7da;
    color: #721c24;
}

.question-item .options-list {
    list-style: none;
    margin: 12px 0 0 0;
}

.question-item .options-list li {
    padding: 6px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    display: flex;
    align-items: center;
    font-size: 0.9rem;
}

.question-item .options-list li:last-child {
    border-bottom: none;
}

.option-label {
    font-weight: 600;
    margin-right: 10px;
    color: #1a7de8;
    min-width: 25px;
}

.correct-answer {
    background: #d4edda;
    color: #155724;
    padding: 4px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-left: 12px;
    display: inline-block;
    border: 1px solid #c3e6cb;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal.show {
    display: block;
    background-color: rgba(0,0,0,0.7);
}

.modal-content {
    background: rgba(30, 25, 40, 0.95);
    margin: auto;
    padding: 0;
    border: 1px solid rgba(255, 255, 255, 0.1);
    width: 90%;
    max-width: 800px;
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.4);
    animation: modalSlideIn 0.3s ease-out forwards;
    display: flex;
    flex-direction: column;
    max-height: 90vh;
    overflow-y: auto;
}

#banUntilInput {
    height: 40px;
    padding: 0 10px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h2 {
    color: #fff;
    font-size: 1.5rem;
}

.ban-banner {
    padding: 15px 20px;
    background-color: rgba(217, 83, 79, 0.2);
    border-bottom: 1px solid rgba(217, 83, 79, 0.5);
    color: #f2b8b5;
    text-align: center;
}

.ban-banner h3 {
    margin: 0 0 5px;
    font-size: 1.2rem;
    color: #fff;
}

.ban-banner p {
    margin: 0;
    font-size: 0.9rem;
}

.close {
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
    transition: color 0.3s ease;
}

.close:hover {
    color: #333;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background-color: rgba(0, 0, 0, 0.1);
}

/* Delete Level Modal Styling */
.warning-message {
    text-align: center;
    margin-bottom: 20px;
}

.warning-message i {
    font-size: 3rem;
    color: #f39c12;
    margin-bottom: 10px;
}

.warning-message h3 {
    color: #fff;
    margin: 0;
    font-size: 1.3rem;
}

.delete-consequences {
    background-color: rgba(217, 83, 79, 0.1);
    border: 1px solid rgba(217, 83, 79, 0.3);
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.delete-consequences p {
    color: #fff;
    margin: 0 0 10px 0;
}

.delete-consequences ul {
    list-style: none;
    padding: 0;
    margin: 10px 0;
}

.delete-consequences li {
    color: #f2b8b5;
    margin: 8px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.delete-consequences li i {
    color: #d9534f;
    width: 16px;
}

.warning-text {
    color: #d9534f !important;
    font-weight: bold;
    text-align: center;
    margin-top: 15px !important;
}

/* Rearrange Levels Modal Styling */
.rearrange-modal .modal-content {
    max-width: 900px;
    width: 95%;
}

.rearrange-instructions {
    background-color: rgba(52, 152, 219, 0.1);
    border: 1px solid rgba(52, 152, 219, 0.3);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.rearrange-instructions p {
    color: #5bc0de;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.quick-swap-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.quick-swap-section h3 {
    color: #fff;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.swap-controls {
    display: flex;
    align-items: end;
    gap: 15px;
    flex-wrap: wrap;
}

.swap-input-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.swap-input-group label {
    color: #ccc;
    font-size: 0.9rem;
}

.swap-input-group select {
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
    min-width: 120px;
}

.swap-arrow {
    color: #f39c12;
    font-size: 1.5rem;
    margin: 0 10px;
}

/* Insert Section Styling */
.insert-section {
    margin: 20px 0;
    padding: 20px;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.insert-section h3 {
    color: #fff;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.insert-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: end;
}

.insert-input-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
    min-width: 150px;
}

.insert-input-group label {
    color: #ccc;
    font-size: 0.9rem;
    font-weight: 500;
}

.insert-input-group select {
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 0.9rem;
}

/* Bulk Operations Section */
.bulk-operations-section {
    margin: 20px 0;
    padding: 20px;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.bulk-operations-section h3 {
    color: #fff;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.bulk-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.bulk-controls .btn-warning {
    background-color: rgba(243, 156, 18, 0.8);
    border-color: rgba(243, 156, 18, 0.8);
}

.bulk-controls .btn-warning:hover {
    background-color: rgba(243, 156, 18, 1);
    border-color: rgba(243, 156, 18, 1);
}

.bulk-controls .btn-info {
    background-color: rgba(23, 162, 184, 0.8);
    border-color: rgba(23, 162, 184, 0.8);
}

.bulk-controls .btn-info:hover {
    background-color: rgba(23, 162, 184, 1);
    border-color: rgba(23, 162, 184, 1);
}

/* Level Management Section */
.level-management-section {
    margin: 20px 0;
}

.level-management-section h3 {
    color: #fff;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.level-management-info {
    margin-bottom: 15px;
    padding: 10px 15px;
    background-color: rgba(23, 162, 184, 0.1);
    border: 1px solid rgba(23, 162, 184, 0.3);
    border-radius: 6px;
}

.level-management-info p {
    color: #ccc;
    margin: 0;
    font-size: 0.9rem;
}

.level-management-info i {
    color: #17a2b8;
    margin-right: 8px;
}

.insert-levels {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 15px;
    background-color: rgba(0, 0, 0, 0.2);
}

.insert-level-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 15px;
    margin: 8px 0;
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.insert-level-item:hover {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
}

.level-item-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.level-item-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.level-item-title {
    color: #fff;
    font-weight: bold;
    font-size: 1rem;
}

.level-item-meta {
    color: #ccc;
    font-size: 0.85rem;
}

.level-position-indicator {
    background-color: rgba(52, 152, 219, 0.8);
    color: #fff;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
    min-width: 60px;
    text-align: center;
}

/* Level Actions */
.level-actions {
    display: flex;
    gap: 5px;
    align-items: center;
}

.btn-small {
    padding: 6px 8px;
    font-size: 0.8rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: rgba(52, 152, 219, 0.8);
    color: #fff;
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-small:hover {
    background-color: rgba(52, 152, 219, 1);
    transform: translateY(-1px);
}

.btn-small:active {
    transform: translateY(0);
}

.btn-small i {
    font-size: 0.9rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .swap-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .swap-arrow {
        align-self: center;
        transform: rotate(90deg);
    }

    .insert-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .insert-input-group {
        min-width: auto;
    }

    .bulk-controls {
        flex-direction: column;
    }

    .bulk-controls button {
        width: 100%;
        margin-bottom: 5px;
    }

    .level-actions {
        flex-wrap: wrap;
        gap: 3px;
    }

    .btn-small {
        min-width: 28px;
        height: 28px;
        font-size: 0.7rem;
    }

    .rearrange-modal .modal-content {
        width: 98%;
        margin: 10px auto;
    }
}

/* Level Questions Modal Styling */
.level-questions-modal .modal-content {
    max-width: 1400px;
    width: 95%;
    max-height: 90vh;
    border-radius: 16px;
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4);
    background: linear-gradient(145deg, rgba(30, 25, 40, 0.98), rgba(25, 20, 35, 0.98));
    border: 1px solid rgba(52, 152, 219, 0.2);
}

.level-questions-modal .modal-body {
    max-height: 75vh;
    overflow-y: auto;
    padding: 30px;
    display: flex;
    flex-direction: column;
}

.level-questions-modal .modal-header {
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.25), rgba(52, 152, 219, 0.15));
    border-bottom: 1px solid rgba(52, 152, 219, 0.3);
    padding: 25px 30px;
    border-radius: 16px 16px 0 0;
    position: relative;
}

.close-top-right {
    position: absolute;
    top: 15px;
    right: 20px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 2.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 8px;
    border-radius: 8px;
    line-height: 1;
    z-index: 10;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-top-right:hover {
    color: #fff;
    background: rgba(255, 255, 255, 0.15);
    transform: scale(1.1);
}

.modal-title-section {
    padding-right: 60px; /* Make space for the close button */
}

.modal-title-section h2 {
    color: #fff;
    font-size: 1.8rem;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    margin: 0 0 5px 0;
}

.modal-subtitle {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.95rem;
    margin: 0;
    font-weight: 400;
}

.level-info-summary {
    margin-bottom: 20px;
    padding: 0;
    background: none;
    border: none;
    border-radius: 0;
}

.total-questions-display {
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.15), rgba(52, 152, 219, 0.08));
    border: 1px solid rgba(52, 152, 219, 0.25);
    border-radius: 12px;
    padding: 15px 20px;
    backdrop-filter: blur(15px);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.questions-count-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
    font-weight: 500;
}

.questions-count-number {
    color: #3498db;
    font-size: 1.2rem;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.level-questions-container {
    flex: 1;
    overflow-y: visible;
    padding-right: 0;
}

.level-question-item {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.03));
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    margin-bottom: 20px;
    padding: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    overflow: hidden;
    position: relative;
}

.level-question-item:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.06));
    border-color: rgba(52, 152, 219, 0.4);
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
}

.level-question-item.has-pending-changes {
    border-color: rgba(243, 156, 18, 0.6);
    background: linear-gradient(135deg, rgba(243, 156, 18, 0.15), rgba(243, 156, 18, 0.08));
}

.level-question-item.has-pending-changes::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #f39c12, #e67e22);
}

.level-question-item.expanded {
    border-color: rgba(52, 152, 219, 0.5);
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.12), rgba(52, 152, 219, 0.06));
}

.level-question-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 20px 25px 15px 25px;
    gap: 20px;
    transition: all 0.3s ease;
}

.level-question-text {
    color: #fff;
    font-weight: 600;
    font-size: 1.15rem;
    flex: 1;
    line-height: 1.6;
    margin: 0;
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.question-number {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    width: 28px;
    height: 28px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.85rem;
    font-weight: bold;
    flex-shrink: 0;
    margin-top: 2px;
}

.question-text-content {
    flex: 1;
}

.level-question-actions {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-shrink: 0;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.level-question-item:hover .level-question-actions {
    opacity: 1;
}

.level-question-meta {
    display: flex;
    gap: 12px;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.pending-indicator {
    color: #f39c12;
    font-size: 0.8rem;
    margin-left: 8px;
}

.level-question-options {
    margin-top: 15px;
}

.level-question-options-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 12px;
}

.level-question-options-list li {
    color: #e0e0e0;
    font-size: 1rem;
    padding: 12px 16px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.03));
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.2s ease;
}

.level-question-options-list li:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.06));
    border-color: rgba(255, 255, 255, 0.2);
}

.level-question-matching-answer {
    color: #e0e0e0;
    font-size: 1rem;
    margin-top: 15px;
    padding: 12px 16px;
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.15), rgba(46, 204, 113, 0.05));
    border: 1px solid rgba(46, 204, 113, 0.3);
    border-radius: 8px;
    font-weight: 500;
}

.empty-level-questions {
    text-align: center;
    color: #ccc;
    padding: 60px 20px;
    font-style: italic;
    font-size: 1.1rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
    border: 2px dashed rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    margin: 20px 0;
}

/* Responsive adjustments for level questions modal */
@media (max-width: 1200px) {
    .level-questions-modal .modal-content {
        max-width: 95%;
        width: 95%;
    }

    .level-question-options-list {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .level-questions-modal .modal-content {
        width: 98%;
        margin: 10px auto;
        max-height: 95vh;
    }

    .level-questions-modal .modal-header {
        padding: 20px;
    }

    .level-questions-modal .modal-header h2 {
        font-size: 1.5rem;
    }

    .level-questions-modal .modal-body {
        padding: 20px;
    }

    .close-top-right {
        top: 10px;
        right: 15px;
        font-size: 2rem;
        width: 35px;
        height: 35px;
        padding: 6px;
    }

    .modal-title-section {
        padding-right: 50px;
    }

    .level-stats {
        gap: 15px;
        flex-direction: column;
    }

    .level-stat-item {
        padding: 10px 15px;
    }

    .level-question-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .level-question-actions {
        align-self: flex-end;
    }

    .level-question-text {
        font-size: 1rem;
    }

    .level-question-options-list {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .level-question-options-list li {
        padding: 10px 12px;
        font-size: 0.95rem;
    }
}

/* All Questions Modal Styling */
.all-questions-modal .modal-content {
    max-width: 1600px;
    width: 98%;
    max-height: 95vh;
    display: flex;
    flex-direction: column;
}

.all-questions-modal .modal-body {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    padding: 25px;
}

.questions-summary {
    margin-bottom: 25px;
    padding: 20px;
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.15), rgba(52, 152, 219, 0.05));
    border: 1px solid rgba(52, 152, 219, 0.3);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    flex-shrink: 0;
}

.summary-stats {
    display: flex;
    justify-content: center;
    gap: 60px;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 20px 30px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-width: 120px;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #5bc0de;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
    color: #e0e0e0;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    font-weight: 500;
}

.questions-filter {
    display: flex;
    gap: 20px;
    margin-bottom: 25px;
    padding: 20px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    flex-wrap: wrap;
    flex-shrink: 0;
}

.questions-filter input,
.questions-filter select {
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
    flex: 1;
    min-width: 150px;
}

.questions-filter input::placeholder {
    color: #ccc;
}

.all-questions-container {
    flex: 1;
    overflow-y: auto;
    padding-right: 10px;
    min-height: 0;
}

.question-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.03));
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 12px;
    margin-bottom: 20px;
    padding: 20px;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.question-card:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12), rgba(255, 255, 255, 0.06));
    border-color: rgba(52, 152, 219, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.question-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
    gap: 15px;
}

.question-card-title {
    color: #fff;
    font-weight: bold;
    font-size: 1rem;
    flex: 1;
    line-height: 1.4;
}

.question-card-meta {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-shrink: 0;
}

.level-badge {
    background-color: rgba(52, 152, 219, 0.8);
    color: #fff;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
}

.level-question-meta .type-badge {
    background: linear-gradient(135deg, rgba(243, 156, 18, 0.9), rgba(243, 156, 18, 0.7));
    color: #fff;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid rgba(243, 156, 18, 0.3);
}

.question-options {
    margin-top: 10px;
}

.question-options-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 8px;
}

.question-options-list li {
    color: #ccc;
    font-size: 0.9rem;
    padding: 5px 10px;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.level-question-options-list .option-label {
    color: #5bc0de;
    font-weight: bold;
    min-width: 25px;
    font-size: 1.1rem;
    background: rgba(91, 192, 222, 0.2);
    padding: 4px 8px;
    border-radius: 6px;
    text-align: center;
}

.level-question-options-list .option-text {
    flex: 1;
    line-height: 1.4;
}

.level-question-options-list .correct-answer {
    color: #5cb85c;
    font-weight: bold;
    margin-left: auto;
    background: rgba(92, 184, 92, 0.2);
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 1.1rem;
}

.matching-answer {
    color: #ccc;
    font-size: 0.9rem;
    margin-top: 10px;
    padding: 8px 12px;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
}

.no-questions-found {
    text-align: center;
    color: #ccc;
    padding: 40px 20px;
    font-style: italic;
}

/* Responsive adjustments for all questions modal */
@media (max-width: 1400px) {
    .all-questions-modal .modal-content {
        max-width: 95%;
        width: 95%;
    }
}

@media (max-width: 768px) {
    .all-questions-modal .modal-content {
        width: 98%;
        margin: 10px auto;
        max-height: 95vh;
    }

    .summary-stats {
        gap: 30px;
        flex-direction: column;
        align-items: center;
    }

    .questions-filter {
        flex-direction: column;
        gap: 15px;
    }

    .questions-filter input,
    .questions-filter select {
        min-width: auto;
    }

    .question-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .question-card-meta {
        align-self: flex-end;
    }

    .question-options-list {
        grid-template-columns: 1fr;
    }
}

.modal-actions {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background-color: rgba(0, 0, 0, 0.2);
}

.action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    border: 1px solid;
    color: rgba(255, 255, 255, 0.8);
    padding: 8px 16px;
    margin: 0 10px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
}

.action-btn i {
    margin-right: 8px;
}

.action-btn:hover {
    color: white;
}

.ban-btn {
    border-color: #f0ad4e;
    color: #f0ad4e;
}

.ban-btn:hover {
    background-color: #f0ad4e;
    box-shadow: 0 0 10px rgba(240, 173, 78, 0.5);
}

.unban-btn {
    border-color: #5cb85c;
    color: #5cb85c;
}

.unban-btn:hover {
    background-color: #5cb85c;
    box-shadow: 0 0 10px rgba(92, 184, 92, 0.5);
}

.delete-btn {
    border-color: #d9534f;
    color: #d9534f;
}

.delete-btn:hover {
    background-color: #d9534f;
    box-shadow: 0 0 10px rgba(217, 83, 79, 0.5);
}

.approve-btn {
    border-color: #28a745;
    color: #28a745;
}

.approve-btn:hover {
    background-color: #28a745;
    box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
}

.reject-btn {
    border-color: #ffc107;
    color: #ffc107;
}

.reject-btn:hover {
    background-color: #ffc107;
    color: #212529;
    box-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
}

.disapprove-btn {
    border-color: #e74c3c;
    color: #e74c3c;
}

.disapprove-btn:hover {
    background-color: #e74c3c;
    box-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
}


/* User Details Modal */
.user-detail-section {
    margin-bottom: 25px;
}

.user-detail-section h3 {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 15px;
    font-size: 1.2rem;
    border-bottom: 2px solid #1a7de8;
    padding-bottom: 5px;
}

.user-basic-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
}

.info-item {
    background: rgba(255, 255, 255, 0.05);
    padding: 15px;
    border-radius: 8px;
    overflow: hidden;
    word-wrap: break-word;
}

.info-label {
    font-weight: 600;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.info-value {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    word-break: break-word;
}

.progress-section {
    margin: 20px 0;
}

.progress-item {
    margin-bottom: 20px;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.progress-label {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
}

.progress-value {
    font-weight: 600;
    color: #1a7de8;
}

.progress-bar {
    width: 100%;
    height: 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #1a7de8, #ff9e3f);
    border-radius: 6px;
    transition: width 0.3s ease;
}

.tier-display {
    text-align: center;
    padding: 20px;
    background: linear-gradient(135deg, #1a7de8, #1565C0);
    color: white;
    border-radius: 12px;
    margin: 20px 0;
}

.tier-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.tier-exp {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    background-color: rgba(15, 12, 20, 0.95);
    color: rgba(255, 255, 255, 0.95);
    font-weight: 500;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #1a7de8;
}

.form-group select option {
    background-color: rgb(15, 12, 20);
    color: rgba(255, 255, 255, 0.95);
    padding: 8px;
    font-weight: 500;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-container {
        flex-direction: column;
        overflow-x: hidden;
        width: 100%;
    }

    .sidebar {
        width: 100%;
        min-width: 100%;
        padding: 15px 0;
    }

    .nav-menu {
        display: flex;
        overflow-x: auto;
        padding: 10px 0;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none; /* Firefox */
    }
    
    .nav-menu::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Edge */
    }

    .nav-item {
        margin: 0 5px;
        white-space: nowrap;
    }

    .main-content {
        padding: 20px 15px;
        width: 100%;
        max-width: 100%;
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
        width: 100%;
    }
    
    .search-bar {
        max-width: 100%;
        width: 100%;
    }

    .users-grid {
        grid-template-columns: 1fr;
        width: 100%;
    }

    .modal-content {
        width: 95%;
        max-width: 95%;
        margin: 20px auto;
    }

    .user-basic-info {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }

    .level-header {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .level-title-row {
        flex-direction: row;
        justify-content: space-between;
    }

    .level-meta {
        justify-content: flex-start;
    }

    .level-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .question-item .question-header {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .question-item .question-actions {
        justify-content: center;
    }
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
    min-width: 300px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    animation: slideIn 0.3s ease;
}

.notification-success {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.notification-error {
    background: linear-gradient(135deg, #dc3545, #e74c3c);
}

.notification-info {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
}

.notification button {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.notification button:hover {
    background: rgba(255, 255, 255, 0.2);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@media (max-width: 768px) {
    .notification {
        right: 10px;
        left: 10px;
        min-width: auto;
    }
}

/* Ban User Modal Styling */
#banUserModal .modal-body {
    padding: 20px;
}

#banUserForm {
    display: flex;
    flex-direction: column;
}

#banUserForm label {
    margin-bottom: 10px;
    color: #ffffff; /* Lighter color for consistency */
    opacity: 0.8;
}

#banUserForm input[type="datetime-local"] {
    width: 100%;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: 4px;
}

#banUserForm input[type="date"] {
    width: 100%;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: 4px;
    height: 40px;
}

#banErrorMsg {
    color: #d9534f; /* A standard error red */
    display: none;
    margin-top: 10px;
    font-size: 0.9rem;
}

#banUserForm div {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

#banUserForm button {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.3s ease;
}

#banUserForm input[type="date"] {
    width: 100%;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: 5px;
    font-size: 1rem;
    height: 40px; /* Ensure consistent height */
}

#banUserForm button[onclick*="confirmBanUser"] {
    background-color: #d9534f;
    color: white;
}

#banUserForm button[onclick*="confirmBanUser"]:hover {
    background-color: #c9302c;
}

#banUserForm button[onclick*="closeModal"] {
    background-color: #f0f0f0;
    color: #333;
}

#banUserForm button[onclick*="closeModal"]:hover {
    background-color: #e0e0e0;
}

/* Bulk Operations Styles */
.bulk-operations {
    background: rgba(30, 25, 40, 0.8);
    border-radius: 12px;
    padding: 15px 20px;
    margin-bottom: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.bulk-controls {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

#selectedCount {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    margin-right: 10px;
}

.bulk-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    border: 1px solid;
    color: rgba(255, 255, 255, 0.8);
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
}

.bulk-btn i {
    margin-right: 6px;
}

.bulk-btn:hover {
    color: white;
    transform: translateY(-1px);
}

.bulk-btn.approve-btn {
    border-color: #28a745;
    color: #28a745;
}

.bulk-btn.approve-btn:hover {
    background-color: #28a745;
    color: white;
    box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
}

.bulk-btn.reject-btn {
    border-color: #ffc107;
    color: #ffc107;
}

.bulk-btn.reject-btn:hover {
    background-color: #ffc107;
    color: white;
    box-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
}

.bulk-btn.clear-btn {
    border-color: #6c757d;
    color: #6c757d;
}

.bulk-btn.clear-btn:hover {
    background-color: #6c757d;
    box-shadow: 0 0 10px rgba(108, 117, 125, 0.5);
}

/* Enhanced User Selection Checkbox */
.user-selection {
    position: absolute;
    top: 12px;
    left: 12px;
    z-index: 10;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 8px;
    padding: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.user-checkbox {
    width: 20px;
    height: 20px;
    cursor: pointer;
    accent-color: #1a7de8;
    margin: 0;
    border-radius: 4px;
}

.user-selection:hover {
    background: rgba(26, 125, 232, 0.2);
    border-color: rgba(26, 125, 232, 0.5);
    transform: scale(1.05);
}

/* Edit and Activate/Deactivate Button Styles */
.edit-btn {
    border-color: #17a2b8;
    color: #17a2b8;
}

.edit-btn:hover {
    background-color: #17a2b8;
    box-shadow: 0 0 10px rgba(23, 162, 184, 0.5);
}

.activate-btn {
    border-color: #28a745;
    color: #28a745;
}

.activate-btn:hover {
    background-color: #28a745;
    box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
}

.deactivate-btn {
    border-color: #fd7e14;
    color: #fd7e14;
}

.deactivate-btn:hover {
    background-color: #fd7e14;
    box-shadow: 0 0 10px rgba(253, 126, 20, 0.5);
}

/* Deactivation Banner */
.deactivation-banner {
    padding: 15px 20px;
    background-color: rgba(253, 126, 20, 0.2);
    border-bottom: 1px solid rgba(253, 126, 20, 0.5);
    color: #ffc9a6;
    text-align: center;
}

.deactivation-banner h3 {
    margin: 0 0 5px;
    font-size: 1.2rem;
    color: #fff;
}

.deactivation-banner p {
    margin: 0;
    font-size: 0.9rem;
}

/* Form Button Styles */
.save-btn {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.save-btn:hover {
    background-color: #218838;
    transform: translateY(-1px);
}

.cancel-btn {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.cancel-btn:hover {
    background-color: #5a6268;
    transform: translateY(-1px);
}

/* Responsive adjustments for bulk operations */
@media (max-width: 768px) {
    .bulk-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .bulk-btn {
        justify-content: center;
        width: 100%;
    }

    #selectedCount {
        text-align: center;
        margin-right: 0;
    }
}

/* Approval Status Badges */
.approval-status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
    display: inline-block;
}

.approval-status-badge.approved {
    background: #28a745;
    color: white;
}

.approval-status-badge.pending {
    background: #ffc107;
    color: #212529;
}

.approval-status-badge.rejected {
    background: #dc3545;
    color: white;
}

.ban-status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
    display: inline-block;
    background: #6c757d;
    color: white;
}

.ban-status-badge.banned {
    background: #dc3545;
    color: white;
}

/* Modal Footer Styling */
.level-questions-modal .modal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    background: linear-gradient(135deg, rgba(30, 25, 40, 0.8), rgba(25, 20, 35, 0.8));
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0 0 16px 16px;
}

.modal-footer-left {
    flex: 1;
}

.footer-info {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    font-style: italic;
}

.modal-footer-right {
    display: flex;
    align-items: center;
}

/* Question Content Styling */
.question-content {
    padding: 0 25px 20px 25px;
    display: block;
}

.question-type-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-bottom: 15px;
}

.question-type-badge.matching {
    background: linear-gradient(135deg, #e67e22, #d35400);
}

.question-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 12px;
    margin-top: 15px;
}

.question-option {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 12px 15px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.question-option.correct {
    background: linear-gradient(135deg, rgba(39, 174, 96, 0.2), rgba(34, 153, 84, 0.2));
    border-color: rgba(39, 174, 96, 0.4);
    color: #2ecc71;
}

.question-option:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
}


