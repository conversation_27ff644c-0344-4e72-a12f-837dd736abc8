// Global variables
let gameLevels = [];
let filteredGameLevels = [];
let gameContent = [];

// API Configuration
const API_BASE_URL = '../../php/admin/';

// Constants
const MAX_QUESTIONS_PER_LEVEL = 50;

// Pending changes system
let pendingChanges = new Map(); // Map<levelID, Map<questionID, questionData>>
let originalQuestions = new Map(); // Map<questionID, originalQuestionData>

// Track expanded levels to preserve state during refresh
let expandedLevels = new Set(); // Set<levelID>

// Initialize game content data
async function initializeGameData() {
    try {
        await loadGameContent();
        displayGameLevels();
    } catch (error) {
        console.error('Error initializing game data:', error);
        showNotification('Error loading game content from database', 'error');
    }
}

// Load game content from API
async function loadGameContent() {
    try {
        const response = await fetch(`${API_BASE_URL}game_content_api.php?action=get_all_content`);
        const result = await response.json();

        if (result.success) {
            gameContent = result.data;
            processGameLevels();
        } else {
            throw new Error(result.error || 'Failed to load game content');
        }
    } catch (error) {
        console.error('Error loading game content:', error);
        showNotification(error.message || 'Error loading game content', 'error');
        gameContent = [];
        gameLevels = [];
        filteredGameLevels = [];
    }
}

// Process game content into levels
function processGameLevels() {
    const levelGroups = {};

    gameContent.forEach(content => {
        const levelNum = content.level_number;
        if (!levelGroups[levelNum]) {
            levelGroups[levelNum] = {
                levelID: levelNum,
                levelName: `Level ${levelNum}`,
                canEdit: content.can_edit == 1, // Convert to boolean
                questions: []
            };
        }

        // Update canEdit to false if any question in the level has can_edit = 0
        if (content.can_edit == 0) {
            levelGroups[levelNum].canEdit = false;
        }

        levelGroups[levelNum].questions.push({
            id: content.content_id,
            question: content.question_text,
            options: [content.option1, content.option2, content.option3, content.option4],
            correctAnswer: content.correct_answer,
            canEdit: content.can_edit == 1,
            quizType: content.quiz_type || 'Multiple Choice'
        });
    });

    gameLevels = Object.values(levelGroups).sort((a, b) => a.levelID - b.levelID);
    filteredGameLevels = [...gameLevels];

    if (gameLevels.length === 0) {
        gameLevels = [];
        filteredGameLevels = [];
    }

    // Store original questions for comparison
    storeOriginalQuestions();
}

// Store original questions for pending changes comparison
function storeOriginalQuestions() {
    originalQuestions.clear();
    gameLevels.forEach(level => {
        level.questions.forEach(question => {
            originalQuestions.set(question.id, {
                id: question.id,
                question: question.question,
                options: [...question.options],
                correctAnswer: question.correctAnswer,
                levelID: level.levelID
            });
        });
    });
}

// Check if a question has pending changes
function hasQuestionPendingChanges(questionId, levelID) {
    const levelChanges = pendingChanges.get(levelID);
    return levelChanges && levelChanges.has(questionId);
}

// Get pending changes for a question
function getPendingQuestion(questionId, levelID) {
    const levelChanges = pendingChanges.get(levelID);
    if (levelChanges && levelChanges.has(questionId)) {
        return levelChanges.get(questionId);
    }
    return null;
}

// Add or update pending changes for a question
function setPendingQuestion(questionId, levelID, questionData) {
    if (!pendingChanges.has(levelID)) {
        pendingChanges.set(levelID, new Map());
    }
    const levelChanges = pendingChanges.get(levelID);
    levelChanges.set(questionId, questionData);
}

// Remove pending changes for a question
function removePendingQuestion(questionId, levelID) {
    const levelChanges = pendingChanges.get(levelID);
    if (levelChanges) {
        levelChanges.delete(questionId);
        if (levelChanges.size === 0) {
            pendingChanges.delete(levelID);
        }
    }
}

// Check if a level has any pending changes
function hasLevelPendingChanges(levelID) {
    const levelChanges = pendingChanges.get(levelID);
    return levelChanges && levelChanges.size > 0;
}

// Get the current question data (pending or original)
function getCurrentQuestionData(questionId, levelID) {
    const pendingQuestion = getPendingQuestion(questionId, levelID);
    if (pendingQuestion) {
        return pendingQuestion;
    }

    const level = gameLevels.find(l => l.levelID === levelID);
    if (level) {
        const question = level.questions.find(q => q.id === questionId);
        if (question) {
            return question;
        }
    }

    return null;
}

// Display game levels
function displayGameLevels() {
    const levelsContainer = document.getElementById('levelsContainer');
    levelsContainer.innerHTML = '';

    if (filteredGameLevels.length === 0) {
        const isSearching = document.getElementById('levelSearch').value.trim() !== '';
        levelsContainer.innerHTML = `
            <div class="empty-levels-state">
                <div class="empty-icon">
                    <i class="fas fa-${isSearching ? 'search' : 'gamepad'}"></i>
                </div>
                <h3>${isSearching ? 'No Levels Found' : 'No Game Levels Found'}</h3>
                <p>${isSearching ? 'No levels match your search criteria. Try different keywords.' : 'No levels with questions have been created yet. Add some questions to create levels.'}</p>
                ${!isSearching ? '<button class="btn-primary" onclick="showAddQuestionModal(1)"><i class="fas fa-plus"></i> Add First Question</button>' : ''}
            </div>
        `;
        return;
    }

    filteredGameLevels.forEach(level => {
        const levelCard = createLevelCard(level);
        levelsContainer.appendChild(levelCard);
    });
}

// Store the current expanded state of levels
function storeExpandedState() {
    expandedLevels.clear();
    filteredGameLevels.forEach(level => {
        const questionsContainer = document.getElementById(`level-questions-${level.levelID}`);
        if (questionsContainer && questionsContainer.classList.contains('expanded')) {
            expandedLevels.add(level.levelID);
        }
    });
}

// Restore the expanded state of levels
function restoreExpandedState() {
    expandedLevels.forEach(levelID => {
        const questionsContainer = document.getElementById(`level-questions-${levelID}`);
        const dropdownToggle = document.getElementById(`dropdown-toggle-${levelID}`);

        if (questionsContainer && dropdownToggle) {
            questionsContainer.classList.remove('collapsed');
            questionsContainer.classList.add('expanded');
            dropdownToggle.innerHTML = '<i class="fas fa-chevron-up"></i>';
        }
    });
}

// Create level card HTML
function createLevelCard(level) {
    const card = document.createElement('div');
    card.className = 'level-card';

    const questionsHtml = level.questions.map(question => {
        // Check if this question has pending changes
        const hasPendingChanges = hasQuestionPendingChanges(question.id, level.levelID);
        const currentQuestion = hasPendingChanges ? getPendingQuestion(question.id, level.levelID) : question;

        let optionsHtml = '';
        if ((currentQuestion.quizType || 'Multiple Choice') === 'Multiple Choice') {
            const optionLabels = ['A', 'B', 'C', 'D'];
            optionsHtml = currentQuestion.options.map((option, index) => {
                // Debug logging
                console.log('Question:', currentQuestion.question);
                console.log('Correct Answer:', currentQuestion.correctAnswer);
                console.log('Options:', currentQuestion.options);

                let correctIndex = -1;

                // Check if correctAnswer is a letter (A, B, C, D)
                if (typeof currentQuestion.correctAnswer === 'string' &&
                    optionLabels.includes(currentQuestion.correctAnswer.toUpperCase())) {
                    correctIndex = optionLabels.indexOf(currentQuestion.correctAnswer.toUpperCase());
                }
                // Check if correctAnswer is a number (1, 2, 3, 4)
                else if (typeof currentQuestion.correctAnswer === 'number' &&
                         currentQuestion.correctAnswer >= 1 && currentQuestion.correctAnswer <= 4) {
                    correctIndex = currentQuestion.correctAnswer - 1;
                }
                // Check if correctAnswer is a string number ("1", "2", "3", "4")
                else if (typeof currentQuestion.correctAnswer === 'string' &&
                         !isNaN(currentQuestion.correctAnswer) &&
                         parseInt(currentQuestion.correctAnswer) >= 1 &&
                         parseInt(currentQuestion.correctAnswer) <= 4) {
                    correctIndex = parseInt(currentQuestion.correctAnswer) - 1;
                }
                // Check if correctAnswer matches the actual option text
                else if (typeof currentQuestion.correctAnswer === 'string') {
                    correctIndex = currentQuestion.options.findIndex(opt =>
                        opt && opt.toLowerCase().trim() === currentQuestion.correctAnswer.toLowerCase().trim()
                    );
                }

                const isCorrect = index === correctIndex;
                console.log(`Option ${index} (${optionLabels[index]}): "${option}" - Correct: ${isCorrect} (correctIndex: ${correctIndex})`);

                return `<li>
                    <span class="option-label">${optionLabels[index]}:</span>
                    <span class="option-text">${option}</span>${isCorrect ? '<span class="correct-answer">✓ Correct</span>' : ''}
                </li>`;
            }).join('');
        }

        return `<div class="question-item ${hasPendingChanges ? 'has-pending-changes' : ''}">
            <div class="question-header">
                <div class="question-text">
                    ${currentQuestion.question}
                    ${hasPendingChanges ? '<span class="pending-indicator" title="Has unsaved changes"><i class="fas fa-circle"></i></span>' : ''}
                </div>
                <div class="question-actions">
                    <button class="btn-small btn-warning" onclick="editQuestion(${question.id}, ${level.levelID})" ${!question.canEdit ? 'disabled title="Editing disabled for this question"' : ''}>
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-small btn-danger" onclick="deleteQuestion(${question.id})" ${!question.canEdit ? 'disabled title="Deletion disabled for this question"' : ''}>
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="question-meta">
                <span>Level ${level.levelID}</span>
                ${hasPendingChanges ? '<span class="pending-changes-text">Unsaved changes</span>' : ''}
                <span class="quiz-type-label">${currentQuestion.quizType || 'Multiple Choice'}</span>
            </div>
            ${(currentQuestion.quizType || 'Multiple Choice') === 'Multiple Choice' ? `<ul class="options-list">${optionsHtml}</ul>` : ''}
            ${(currentQuestion.quizType || 'Multiple Choice') === 'Matching Type' ? `<div class="matching-answer"><b>Correct Answer:</b> ${currentQuestion.correctAnswer}</div>` : ''}
        </div>`;
    }).join('');

    // Check if this level has pending changes
    const levelHasPendingChanges = hasLevelPendingChanges(level.levelID);

    card.innerHTML = `
        <div class="level-header" onclick="showLevelQuestionsModal(${level.levelID})" style="cursor: pointer;" title="Click to view questions in this level">
            <div class="level-info">
                <div class="level-title-row">
                    <h3>${level.levelName} ${levelHasPendingChanges ? '<span class="level-pending-indicator" title="Has unsaved changes"><i class="fas fa-exclamation-circle"></i></span>' : ''} ${!level.canEdit ? '<span class="edit-disabled-indicator" title="Editing disabled"><i class="fas fa-lock"></i></span>' : ''}</h3>
                    <div class="level-meta">
                        <span class="questions-count">${level.questions.length}/${MAX_QUESTIONS_PER_LEVEL} questions</span>
                        ${levelHasPendingChanges ? '<span class="pending-changes-count">Unsaved changes</span>' : ''}
                        ${!level.canEdit ? '<span class="edit-disabled-text">Read-only</span>' : ''}
                        ${level.questions.length >= MAX_QUESTIONS_PER_LEVEL ? '<span class="limit-reached-text">Limit reached</span>' : ''}
                    </div>
                </div>
            </div>
            <div class="level-header-actions" onclick="event.stopPropagation()">
                ${levelHasPendingChanges && level.canEdit ? `
                    <button class="btn-small btn-success" onclick="saveLevelChanges(${level.levelID})" title="Save all changes for this level">
                        <i class="fas fa-save"></i> Save Changes
                    </button>
                    <button class="btn-small btn-secondary" onclick="discardLevelChanges(${level.levelID})" title="Discard all changes for this level">
                        <i class="fas fa-undo"></i> Discard
                    </button>
                ` : ''}
                ${level.canEdit ? `
                    <button class="btn-small btn-primary" onclick="showAddQuestionModal(${level.levelID})" title="${level.questions.length >= MAX_QUESTIONS_PER_LEVEL ? 'Maximum questions reached' : 'Add new question'}" ${level.questions.length >= MAX_QUESTIONS_PER_LEVEL ? 'disabled' : ''}>
                        <i class="fas fa-plus"></i> Add Question
                    </button>
                    <button class="btn-small btn-danger" onclick="showDeleteLevelModal(${level.levelID})" title="Delete this level">
                        <i class="fas fa-trash"></i> Delete Level
                    </button>
                ` : ''}
                <button class="btn-small btn-info" onclick="showLevelQuestionsModal(${level.levelID})" title="View questions in modal">
                    <i class="fas fa-eye"></i> View Questions
                </button>
            </div>
        </div>
    `;

    return card;
}

// Search levels
function searchLevels() {
    const searchTerm = document.getElementById('levelSearch').value.toLowerCase();

    if (searchTerm.trim() === '') {
        filteredGameLevels = [...gameLevels];
    } else {
        filteredGameLevels = gameLevels.filter(level => {
            return level.levelID.toString().includes(searchTerm);
        });
    }

    displayGameLevels();
}

// Show Level Questions Modal
let currentLevelID = null;

function showLevelQuestionsModal(levelID) {
    const level = gameLevels.find(l => l.levelID === levelID);
    if (!level) {
        showNotification('Level not found', 'error');
        return;
    }

    currentLevelID = levelID;

    // Update modal title and subtitle
    document.getElementById('levelModalTitle').textContent = `${level.levelName}`;
    document.getElementById('levelModalSubtitle').textContent = `Manage questions for this level`;

    // Update question count
    document.getElementById('levelQuestionCount').textContent = `${level.questions.length}/${MAX_QUESTIONS_PER_LEVEL}`;

    // Display level questions
    displayLevelQuestions(level);

    // Update add question button
    const addBtn = document.getElementById('addQuestionFromLevelBtn2');

    if (level.canEdit) {
        addBtn.style.display = 'inline-flex';
        if (level.questions.length >= MAX_QUESTIONS_PER_LEVEL) {
            addBtn.disabled = true;
            addBtn.title = `Maximum questions reached (${MAX_QUESTIONS_PER_LEVEL})`;
        } else {
            addBtn.disabled = false;
            addBtn.title = 'Add new question';
        }
        addBtn.onclick = () => showAddQuestionModalFromLevel();
    } else {
        addBtn.style.display = 'none';
    }

    // Show modal
    openModal('levelQuestionsModal');
}



// Display level questions in modal
function displayLevelQuestions(level) {
    const container = document.getElementById('levelQuestionsContainer');
    container.innerHTML = '';

    if (level.questions.length === 0) {
        container.innerHTML = `
            <div class="empty-level-questions">
                <div style="text-align: center; padding: 40px; color: rgba(255, 255, 255, 0.6);">
                    <i class="fas fa-question-circle" style="font-size: 3rem; margin-bottom: 20px; opacity: 0.5;"></i>
                    <h3 style="margin-bottom: 10px; color: rgba(255, 255, 255, 0.8);">No Questions Yet</h3>
                    <p style="margin: 0;">
                        ${level.canEdit ?
                            'Click "Add Question" to get started with this level.' :
                            'No questions are available in this level.'
                        }
                    </p>
                </div>
            </div>
        `;
        updateFooterInfo(level);
        return;
    }

    // Create question items
    level.questions.forEach((question, index) => {
        const questionItem = createLevelQuestionItem(question, level, index);
        container.appendChild(questionItem);
    });

    updateFooterInfo(level);
}

// Update footer information
function updateFooterInfo(level) {
    const footerInfo = document.getElementById('questionsFooterInfo');
    if (level.questions.length === 0) {
        footerInfo.textContent = 'No questions added yet';
    } else {
        footerInfo.textContent = `${level.questions.length} question${level.questions.length !== 1 ? 's' : ''} in this level`;
    }
}

// Create level question item element
function createLevelQuestionItem(question, level, questionIndex) {
    const hasPendingChanges = hasQuestionPendingChanges(question.id, level.levelID);
    const currentQuestion = hasPendingChanges ? getPendingQuestion(question.id, level.levelID) : question;

    const item = document.createElement('div');
    item.className = `level-question-item ${hasPendingChanges ? 'has-pending-changes' : ''}`;
    item.setAttribute('data-question-id', question.id);

    let optionsHtml = '';
    if ((currentQuestion.quizType || 'Multiple Choice') === 'Multiple Choice') {
        const optionLabels = ['A', 'B', 'C', 'D'];
        optionsHtml = `
            <div class="question-options">
                ${currentQuestion.options.map((option, index) => {
                    const isCorrect = isCorrectOption(currentQuestion, index);
                    return `<div class="question-option ${isCorrect ? 'correct' : ''}">
                        <strong>${optionLabels[index]}.</strong> ${option}
                    </div>`;
                }).join('')}
            </div>
        `;
    } else {
        optionsHtml = `
            <div class="question-options">
                <div class="question-option correct">
                    <strong>Correct Answer:</strong> ${currentQuestion.correctAnswer}
                </div>
            </div>
        `;
    }

    const questionTypeClass = (currentQuestion.quizType || 'Multiple Choice') === 'Matching Type' ? 'matching' : '';

    item.innerHTML = `
        <div class="level-question-header">
            <div class="level-question-text">
                <div class="question-number">${questionIndex + 1}</div>
                <div class="question-text-content">
                    ${currentQuestion.question}
                    ${hasPendingChanges ? '<span class="pending-indicator" title="Has unsaved changes"><i class="fas fa-circle"></i></span>' : ''}
                </div>
            </div>
            <div class="level-question-actions">
                ${question.canEdit ? `
                    <button class="btn-small btn-warning" onclick="editQuestion(${question.id}, ${level.levelID})" title="Edit question">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-small btn-danger" onclick="deleteQuestion(${question.id})" title="Delete question">
                        <i class="fas fa-trash"></i>
                    </button>
                ` : ''}
            </div>
        </div>
        <div class="question-content" id="question-content-${question.id}">
            <div class="question-type-badge ${questionTypeClass}">
                <i class="fas ${(currentQuestion.quizType || 'Multiple Choice') === 'Matching Type' ? 'fa-link' : 'fa-list-ul'}"></i>
                ${currentQuestion.quizType || 'Multiple Choice'}
            </div>
            ${hasPendingChanges ? '<div class="pending-changes-text"><i class="fas fa-exclamation-circle"></i> Unsaved changes</div>' : ''}
            ${optionsHtml}
        </div>
    `;

    return item;
}

// Show add question modal from level modal
function showAddQuestionModalFromLevel() {
    if (currentLevelID) {
        closeModal('levelQuestionsModal');
        showAddQuestionModal(currentLevelID);
    }
}



// Show All Questions Modal
function showAllQuestionsModal() {
    // Populate statistics
    updateQuestionsStatistics();

    // Populate level filter
    populateLevelFilter();

    // Display all questions
    displayAllQuestions();

    // Clear filters
    document.getElementById('questionsSearch').value = '';
    document.getElementById('levelFilter').value = '';
    document.getElementById('typeFilter').value = '';

    // Show modal
    openModal('allQuestionsModal');
}

// Update questions statistics
function updateQuestionsStatistics() {
    const totalLevels = gameLevels.length;
    let totalQuestions = 0;

    gameLevels.forEach(level => {
        totalQuestions += level.questions.length;
    });

    document.getElementById('totalLevels').textContent = totalLevels;
    document.getElementById('totalQuestions').textContent = totalQuestions;
}

// Populate level filter dropdown
function populateLevelFilter() {
    const levelFilter = document.getElementById('levelFilter');
    levelFilter.innerHTML = '<option value="">All Levels</option>';

    const sortedLevels = [...gameLevels].sort((a, b) => a.levelID - b.levelID);
    sortedLevels.forEach(level => {
        const option = document.createElement('option');
        option.value = level.levelID;
        option.textContent = `Level ${level.levelID}`;
        levelFilter.appendChild(option);
    });
}

// Display all questions
function displayAllQuestions() {
    const container = document.getElementById('allQuestionsContainer');
    container.innerHTML = '';

    // Collect all questions from all levels
    const allQuestions = [];
    gameLevels.forEach(level => {
        level.questions.forEach(question => {
            allQuestions.push({
                ...question,
                levelID: level.levelID,
                levelName: level.levelName
            });
        });
    });

    // Sort questions by level and then by question ID
    allQuestions.sort((a, b) => {
        if (a.levelID !== b.levelID) {
            return a.levelID - b.levelID;
        }
        return a.id - b.id;
    });

    if (allQuestions.length === 0) {
        container.innerHTML = '<div class="no-questions-found">No questions found</div>';
        return;
    }

    // Create question cards
    allQuestions.forEach(question => {
        const questionCard = createQuestionCard(question);
        container.appendChild(questionCard);
    });
}

// Create question card element
function createQuestionCard(question) {
    const card = document.createElement('div');
    card.className = 'question-card';
    card.dataset.levelId = question.levelID;
    card.dataset.questionType = question.quizType || 'Multiple Choice';
    card.dataset.questionText = question.question.toLowerCase();

    let optionsHtml = '';
    if ((question.quizType || 'Multiple Choice') === 'Multiple Choice') {
        const optionLabels = ['A', 'B', 'C', 'D'];
        optionsHtml = `
            <div class="question-options">
                <ul class="question-options-list">
                    ${question.options.map((option, index) => {
                        const isCorrect = isCorrectOption(question, index);
                        return `<li>
                            <span class="option-label">${optionLabels[index]}:</span>
                            <span class="option-text">${option}</span>
                            ${isCorrect ? '<span class="correct-answer">✓</span>' : ''}
                        </li>`;
                    }).join('')}
                </ul>
            </div>
        `;
    } else {
        optionsHtml = `
            <div class="matching-answer">
                <strong>Correct Answer:</strong> ${question.correctAnswer}
            </div>
        `;
    }

    card.innerHTML = `
        <div class="question-card-header">
            <div class="question-card-title">${question.question}</div>
            <div class="question-card-meta">
                <span class="level-badge">Level ${question.levelID}</span>
                <span class="type-badge">${question.quizType || 'Multiple Choice'}</span>
            </div>
        </div>
        ${optionsHtml}
    `;

    return card;
}

// Helper function to determine if an option is correct
function isCorrectOption(question, optionIndex) {
    const optionLabels = ['A', 'B', 'C', 'D'];
    const correctAnswer = question.correctAnswer;

    // Check if correctAnswer is a letter (A, B, C, D)
    if (typeof correctAnswer === 'string' && optionLabels.includes(correctAnswer.toUpperCase())) {
        return optionLabels.indexOf(correctAnswer.toUpperCase()) === optionIndex;
    }

    // Check if correctAnswer is a number (1, 2, 3, 4)
    if (typeof correctAnswer === 'number' && correctAnswer >= 1 && correctAnswer <= 4) {
        return correctAnswer - 1 === optionIndex;
    }

    // Check if correctAnswer is a string number ("1", "2", "3", "4")
    if (typeof correctAnswer === 'string' && !isNaN(correctAnswer)) {
        const num = parseInt(correctAnswer);
        if (num >= 1 && num <= 4) {
            return num - 1 === optionIndex;
        }
    }

    // Check if correctAnswer matches the actual option text
    if (typeof correctAnswer === 'string') {
        return question.options[optionIndex] &&
               question.options[optionIndex].toLowerCase().trim() === correctAnswer.toLowerCase().trim();
    }

    return false;
}

// Filter all questions based on search and filters
function filterAllQuestions() {
    const searchTerm = document.getElementById('questionsSearch').value.toLowerCase();
    const levelFilter = document.getElementById('levelFilter').value;
    const typeFilter = document.getElementById('typeFilter').value;

    const questionCards = document.querySelectorAll('.question-card');
    let visibleCount = 0;

    questionCards.forEach(card => {
        const questionText = card.dataset.questionText;
        const questionLevel = card.dataset.levelId;
        const questionType = card.dataset.questionType;

        const matchesSearch = !searchTerm || questionText.includes(searchTerm);
        const matchesLevel = !levelFilter || questionLevel === levelFilter;
        const matchesType = !typeFilter || questionType === typeFilter;

        if (matchesSearch && matchesLevel && matchesType) {
            card.style.display = 'block';
            visibleCount++;
        } else {
            card.style.display = 'none';
        }
    });

    // Show "no questions found" message if no results
    const container = document.getElementById('allQuestionsContainer');
    const existingNoResults = container.querySelector('.no-questions-found');

    if (visibleCount === 0 && !existingNoResults) {
        const noResults = document.createElement('div');
        noResults.className = 'no-questions-found';
        noResults.textContent = 'No questions match your search criteria';
        container.appendChild(noResults);
    } else if (visibleCount > 0 && existingNoResults) {
        existingNoResults.remove();
    }
}

// Utility: Show/hide option fields based on quiz_type
function updateModalFieldsByQuizType(quizType) {
    const optionFields = document.querySelectorAll('.option-field');
    const correctAnswerInput = document.getElementById('correctAnswer');
    const correctAnswerLabel = correctAnswerInput.previousElementSibling;
    const infoText = correctAnswerInput.nextElementSibling;
    if (quizType === 'Multiple Choice') {
        optionFields.forEach(f => f.style.display = 'block');
        correctAnswerLabel.textContent = 'Correct Answer:';
        correctAnswerInput.placeholder = 'Type the exact answer here';
        if (infoText) infoText.style.display = 'block';
    } else {
        optionFields.forEach(f => f.style.display = 'none');
        correctAnswerLabel.textContent = 'Correct Answers:';
        correctAnswerInput.placeholder = 'Type the correct answer(s) here';
        if (infoText) infoText.style.display = 'none';
    }
}

// Add event listener for quizType select
if (document.getElementById('quizType')) {
    document.getElementById('quizType').addEventListener('change', function() {
        updateModalFieldsByQuizType(this.value);
    });
}

// Show add question modal
function showAddQuestionModal(levelID) {
    const level = gameLevels.find(l => l.levelID === levelID);
    if (!level || !level.canEdit) {
        showNotification('Adding questions is disabled for this level', 'error');
        return;
    }

    // Check if level has reached the maximum question limit
    if (level.questions.length >= MAX_QUESTIONS_PER_LEVEL) {
        showNotification(`Cannot add more questions. Level ${levelID} has reached the maximum limit of ${MAX_QUESTIONS_PER_LEVEL} questions.`, 'error');
        return;
    }

    const modal = document.getElementById('questionModal');
    document.getElementById('questionModalTitle').textContent = 'Add Question';
    document.getElementById('questionForm').reset(); // Reset first
    document.getElementById('levelNumber').value = levelID; // Then set the level number
    document.getElementById('contentId').value = '';
    document.getElementById('quizType').value = 'Multiple Choice';
    updateModalFieldsByQuizType('Multiple Choice');
    modal.classList.add('show');
}

// Edit question
function editQuestion(questionId, levelID) {
    const level = gameLevels.find(l => l.levelID === levelID);
    if (!level) return;

    // Check if editing is allowed for this level
    if (!level.canEdit) {
        showNotification('Editing is disabled for this level', 'error');
        return;
    }

    // Get current question data (pending changes or original)
    const currentQuestion = getCurrentQuestionData(questionId, levelID);
    if (!currentQuestion) return;

    // Check if editing is allowed for this specific question
    const originalQuestion = level.questions.find(q => q.id === questionId);
    if (originalQuestion && !originalQuestion.canEdit) {
        showNotification('Editing is disabled for this question', 'error');
        return;
    }

    const modal = document.getElementById('questionModal');
    document.getElementById('questionModalTitle').textContent = 'Edit Question';
    document.getElementById('contentId').value = questionId;
    document.getElementById('questionText').value = currentQuestion.question;
    document.getElementById('levelNumber').value = levelID;
    // Set quizType and update fields
    document.getElementById('quizType').value = currentQuestion.quizType || 'Multiple Choice';
    updateModalFieldsByQuizType(document.getElementById('quizType').value);
    if ((currentQuestion.quizType || 'Multiple Choice') === 'Multiple Choice') {
        document.getElementById('option1').value = currentQuestion.options[0];
        document.getElementById('option2').value = currentQuestion.options[1];
        document.getElementById('option3').value = currentQuestion.options[2];
        document.getElementById('option4').value = currentQuestion.options[3];
    } else {
        document.getElementById('option1').value = '';
        document.getElementById('option2').value = '';
        document.getElementById('option3').value = '';
        document.getElementById('option4').value = '';
    }
    document.getElementById('correctAnswer').value = currentQuestion.correctAnswer;
    modal.classList.add('show');
}

// Handle add/edit question form submission
async function handleAddQuestion(event) {
    event.preventDefault();

    const quizType = document.getElementById('quizType').value;
    const questionText = document.getElementById('questionText').value;
    const correctAnswer = document.getElementById('correctAnswer').value;
    const levelNumber = parseInt(document.getElementById('levelNumber').value);
    const contentId = document.getElementById('contentId').value;
    let option1 = '', option2 = '', option3 = '', option4 = '';
    if (quizType === 'Multiple Choice') {
        option1 = document.getElementById('option1').value;
        option2 = document.getElementById('option2').value;
        option3 = document.getElementById('option3').value;
        option4 = document.getElementById('option4').value;
        // Validation: correct answer must match one of the options
        if (![option1, option2, option3, option4].includes(correctAnswer)) {
            showNotification('The correct answer must exactly match one of the options above.', 'error');
            return;
        }
    }
    // For Matching Type, only questionText and correctAnswer are required

    const isEdit = contentId !== '';

    if (isEdit) {
        // Store pending changes for existing question
        const questionId = parseInt(contentId);
        const questionData = {
            id: questionId,
            question: questionText,
            options: quizType === 'Multiple Choice' ? [option1, option2, option3, option4] : ['', '', '', ''],
            correctAnswer: correctAnswer,
            levelID: levelNumber,
            quizType: quizType
        };
        setPendingQuestion(questionId, levelNumber, questionData);
        expandedLevels.add(levelNumber);
        showNotification('Question changes saved locally. Click "Save Changes" to apply to database.', 'info');
    } else {
        // For new questions, save immediately (existing behavior)
        const formData = new FormData();
        formData.append('quiz_type', quizType);
        formData.append('question_text', questionText);
        formData.append('option1', option1);
        formData.append('option2', option2);
        formData.append('option3', option3);
        formData.append('option4', option4);
        formData.append('correct_answer', correctAnswer);
        formData.append('level_number', levelNumber);
        try {
            const response = await fetch(`${API_BASE_URL}game_content_api.php?action=add_content`, {
                method: 'POST',
                body: formData
            });
            const result = await response.json();
            if (result.success) {
                showNotification('Question added successfully', 'success');
                await loadGameContent(); // Refresh data
            } else {
                throw new Error(result.error || 'Failed to add question');
            }
        } catch (error) {
            console.error('Error:', error);
            showNotification(error.message || 'An error occurred', 'error');
            return;
        }
    }
    closeModal('questionModal');
    displayGameLevels(); // Refresh display to show pending changes
}

// Save all pending changes for a specific level
async function saveLevelChanges(levelID) {
    const levelChanges = pendingChanges.get(levelID);
    if (!levelChanges || levelChanges.size === 0) {
        showNotification('No pending changes to save for this level.', 'info');
        return;
    }
    const changesArray = Array.from(levelChanges.values());
    let successCount = 0;
    let errorCount = 0;
    const errors = [];
    showNotification(`Saving ${changesArray.length} question changes...`, 'info');
    for (const questionData of changesArray) {
        try {
            const updateData = {
                content_id: questionData.id,
                question_text: questionData.question,
                option1: questionData.options[0],
                option2: questionData.options[1],
                option3: questionData.options[2],
                option4: questionData.options[3],
                correct_answer: questionData.correctAnswer,
                level_number: questionData.levelID,
                quiz_type: questionData.quizType || 'Multiple Choice'
            };
            const response = await fetch(`${API_BASE_URL}game_content_api.php?action=update_content&content_id=${questionData.id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updateData)
            });
            // Check if response is JSON
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                const textResponse = await response.text();
                throw new Error(`Server returned non-JSON response: ${textResponse.substring(0, 200)}...`);
            }
            const result = await response.json();
            if (result.success) {
                successCount++;
                // Remove from pending changes
                removePendingQuestion(questionData.id, levelID);
            } else {
                errorCount++;
                errors.push(`Question ${questionData.id}: ${result.error || 'Unknown error'}`);
            }
        } catch (error) {
            errorCount++;
            errors.push(`Question ${questionData.id}: ${error.message}`);
            console.error(`Error updating question ${questionData.id}:`, error);
        }
    }
    // Show results
    if (successCount > 0 && errorCount === 0) {
        showNotification(`Successfully saved ${successCount} question changes!`, 'success');
        await loadGameContent(); // Refresh data from database
    } else if (successCount > 0 && errorCount > 0) {
        showNotification(`Saved ${successCount} questions, but ${errorCount} failed. Check console for details.`, 'warning');
        console.error('Save errors:', errors);
        await loadGameContent(); // Refresh data from database
    } else {
        showNotification(`Failed to save changes. ${errors.length > 0 ? errors[0] : 'Unknown error'}`, 'error');
        console.error('Save errors:', errors);
    }
    displayGameLevels(); // Refresh display
}

// Discard all pending changes for a specific level
function discardLevelChanges(levelID) {
    const levelChanges = pendingChanges.get(levelID);
    if (!levelChanges || levelChanges.size === 0) {
        showNotification('No pending changes to discard for this level.', 'info');
        return;
    }

    const changeCount = levelChanges.size;

    if (confirm(`Are you sure you want to discard ${changeCount} unsaved changes for this level? This action cannot be undone.`)) {
        // Remove all pending changes for this level
        pendingChanges.delete(levelID);

        showNotification(`Discarded ${changeCount} pending changes.`, 'info');
        displayGameLevels(); // Refresh display to remove pending indicators
    }
}

// Delete question
async function deleteQuestion(questionId) {
    // Find the question and check if deletion is allowed
    let canDelete = false;
    let levelID = null;

    for (const level of gameLevels) {
        const question = level.questions.find(q => q.id === questionId);
        if (question) {
            levelID = level.levelID;
            canDelete = level.canEdit && question.canEdit;
            break;
        }
    }

    if (!canDelete) {
        showNotification('Deletion is disabled for this question', 'error');
        return;
    }

    if (confirm('Are you sure you want to delete this question?')) {
        try {
            const response = await fetch(`${API_BASE_URL}game_content_api.php?action=delete_content&content_id=${questionId}`, {
                method: 'DELETE'
            });
            const result = await response.json();

            if (result.success) {
                showNotification('Question deleted successfully', 'success');

                // Remove the question from the local state
                const level = gameLevels.find(l => l.levelID === levelID);
                if (level) {
                    level.questions = level.questions.filter(q => q.id !== questionId);
                    
                    // Also remove from pending changes if it exists there
                    removePendingQuestion(questionId, levelID);
                }

                // Also remove from original questions to be safe
                originalQuestions.delete(questionId);
                
                // Refresh the display without a full reload
                displayGameLevels();
            } else {
                throw new Error(result.error || 'Failed to delete question');
            }
        } catch (error) {
            console.error('Error:', error);
            showNotification(error.message || 'An error occurred', 'error');
        }
    }
}

// Open modal
function openModal(modalId) {
    document.getElementById(modalId).classList.add('show');
}

// Close modal
function closeModal(modalId) {
    document.getElementById(modalId).classList.remove('show');
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <span>${message}</span>
        <button onclick="this.parentElement.remove()">&times;</button>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeGameData);

// Add form submit event listener
const questionForm = document.getElementById('questionForm');
if (questionForm) {
    questionForm.addEventListener('submit', handleAddQuestion);
}

// Level Management Functions

// Show Add Level Modal
async function showAddLevelModal() {
    try {
        // Get next available level number
        const response = await fetch(`${API_BASE_URL}level_management_api.php?action=get_next_level`);
        const result = await response.json();

        if (result.success) {
            document.getElementById('levelNumber').value = result.next_level;
            document.getElementById('gameCategory').value = '';
            openModal('addLevelModal');
        } else {
            showNotification('Failed to get next level number: ' + (result.error || 'Unknown error'), 'error');
        }
    } catch (error) {
        console.error('Error getting next level:', error);
        showNotification('Error getting next level number', 'error');
    }
}

// Add Level
async function addLevel() {
    const levelNumber = document.getElementById('levelNumber').value;
    const gameCategory = document.getElementById('gameCategory').value;

    if (!levelNumber || !gameCategory) {
        showNotification('Please fill in all required fields', 'error');
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}level_management_api.php?action=add_level`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                level_number: parseInt(levelNumber),
                game_category: gameCategory
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            closeModal('addLevelModal');
            // Refresh the levels display
            await loadGameContent();
            displayGameLevels();
        } else {
            showNotification('Failed to create level: ' + (result.error || 'Unknown error'), 'error');
        }
    } catch (error) {
        console.error('Error creating level:', error);
        showNotification('Error creating level', 'error');
    }
}

// Show Delete Level Modal
function showDeleteLevelModal(levelNumber) {
    // Find the level data
    const level = gameLevels.find(l => l.levelID === levelNumber);
    if (!level) {
        showNotification('Level not found', 'error');
        return;
    }

    // Update modal content
    document.getElementById('deleteLevelNumber').textContent = levelNumber;
    document.getElementById('deleteFileName').textContent = levelNumber;
    document.getElementById('deleteQuestionCount').textContent = level.questions.length;

    // Store level number for confirmation
    document.getElementById('confirmDeleteBtn').setAttribute('data-level', levelNumber);

    // Show modal
    openModal('deleteLevelModal');
}

// Confirm Delete Level (called from modal button)
function confirmDeleteLevel() {
    const levelNumber = document.getElementById('confirmDeleteBtn').getAttribute('data-level');
    if (levelNumber) {
        deleteLevel(parseInt(levelNumber));
        closeModal('deleteLevelModal');
    }
}

// Delete Level
async function deleteLevel(levelNumber) {
    try {
        const response = await fetch(`${API_BASE_URL}level_management_api.php?action=delete_level&level_number=${levelNumber}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            // Refresh the levels display
            await loadGameContent();
            displayGameLevels();
        } else {
            showNotification('Failed to delete level: ' + (result.error || 'Unknown error'), 'error');
        }
    } catch (error) {
        console.error('Error deleting level:', error);
        showNotification('Error deleting level', 'error');
    }
}

// Level Rearrangement Functions

// Show Rearrange Levels Modal
function showRearrangeLevelsModal() {
    // Populate swap dropdowns
    populateSwapDropdowns();

    // Populate insert levels
    populateInsertLevels();

    // Populate move questions dropdowns
    populateMoveQuestionsDropdowns();

    // Show modal
    openModal('rearrangeLevelsModal');
}

// Populate move questions dropdowns
function populateMoveQuestionsDropdowns() {
    const sourceLevel = document.getElementById('sourceQuestionLevel');
    const targetLevel = document.getElementById('targetQuestionLevel');

    // Clear existing options
    sourceLevel.innerHTML = '<option value="">Select source level...</option>';
    targetLevel.innerHTML = '<option value="">Select target level...</option>';

    // Sort levels by level number
    const sortedLevels = [...gameLevels].sort((a, b) => a.levelID - b.levelID);

    // Populate both dropdowns
    sortedLevels.forEach(level => {
        // Only show levels that have questions for source
        if (level.questions.length > 0) {
            const sourceOption = document.createElement('option');
            sourceOption.value = level.levelID;
            sourceOption.textContent = `Level ${level.levelID} (${level.questions.length} questions)`;
            sourceLevel.appendChild(sourceOption);
        }

        // Show all levels for target (including empty ones)
        const targetOption = document.createElement('option');
        targetOption.value = level.levelID;
        targetOption.textContent = `Level ${level.levelID} (${level.questions.length} questions)`;
        targetLevel.appendChild(targetOption);
    });
}

// Populate swap dropdowns with available levels
function populateSwapDropdowns() {
    const swap1 = document.getElementById('swapLevel1');
    const swap2 = document.getElementById('swapLevel2');

    // Clear existing options
    swap1.innerHTML = '<option value="">Select level...</option>';
    swap2.innerHTML = '<option value="">Select level...</option>';

    // Add level options
    gameLevels.forEach(level => {
        const option1 = document.createElement('option');
        option1.value = level.levelID;
        option1.textContent = `Level ${level.levelID} (${level.questions.length} questions)`;
        swap1.appendChild(option1);

        const option2 = document.createElement('option');
        option2.value = level.levelID;
        option2.textContent = `Level ${level.levelID} (${level.questions.length} questions)`;
        swap2.appendChild(option2);
    });
}

// Populate levels for insert before/after functionality
function populateInsertLevels() {
    const container = document.getElementById('insertLevels');
    container.innerHTML = '';

    // Sort levels by level number
    const sortedLevels = [...gameLevels].sort((a, b) => a.levelID - b.levelID);

    sortedLevels.forEach((level, index) => {
        const levelItem = document.createElement('div');
        levelItem.className = 'insert-level-item';
        levelItem.dataset.levelId = level.levelID;

        levelItem.innerHTML = `
            <div class="level-item-info">
                <div class="level-item-details">
                    <div class="level-item-title">Level ${level.levelID}</div>
                    <div class="level-item-meta">${level.questions.length} questions</div>
                </div>
            </div>
            <div class="level-position-indicator">
                Position ${index + 1}
            </div>
            <div class="level-actions">
                <button class="btn-small btn-primary" onclick="moveToTop(${level.levelID})" title="Move to Top">
                    <i class="fas fa-angle-double-up"></i>
                </button>
                <button class="btn-small btn-primary" onclick="moveUp(${level.levelID})" title="Move Up">
                    <i class="fas fa-angle-up"></i>
                </button>
                <button class="btn-small btn-primary" onclick="moveDown(${level.levelID})" title="Move Down">
                    <i class="fas fa-angle-down"></i>
                </button>
                <button class="btn-small btn-primary" onclick="moveToBottom(${level.levelID})" title="Move to Bottom">
                    <i class="fas fa-angle-double-down"></i>
                </button>
            </div>
        `;

        container.appendChild(levelItem);
    });
}

// Level Movement Functions

// Move level to top
async function moveToTop(levelId) {
    await moveLevelToPosition(levelId, 1);
}

// Move level up one position
async function moveUp(levelId) {
    const currentPosition = getCurrentLevelPosition(levelId);
    if (currentPosition > 1) {
        await moveLevelToPosition(levelId, currentPosition - 1);
    }
}

// Move level down one position
async function moveDown(levelId) {
    const currentPosition = getCurrentLevelPosition(levelId);
    const maxPosition = gameLevels.length;
    if (currentPosition < maxPosition) {
        await moveLevelToPosition(levelId, currentPosition + 1);
    }
}

// Move level to bottom
async function moveToBottom(levelId) {
    const maxPosition = gameLevels.length;
    await moveLevelToPosition(levelId, maxPosition);
}

// Get current position of a level
function getCurrentLevelPosition(levelId) {
    const sortedLevels = [...gameLevels].sort((a, b) => a.levelID - b.levelID);
    return sortedLevels.findIndex(level => level.levelID === levelId) + 1;
}

// Move level to specific position
async function moveLevelToPosition(levelId, targetPosition) {
    const sortedLevels = [...gameLevels].sort((a, b) => a.levelID - b.levelID);
    const currentIndex = sortedLevels.findIndex(level => level.levelID === levelId);
    const currentPosition = currentIndex + 1;

    if (currentPosition === targetPosition) {
        showNotification('Level is already in that position', 'info');
        return;
    }

    // Create a simple swap with the level at target position
    const targetIndex = targetPosition - 1;
    if (targetIndex >= 0 && targetIndex < sortedLevels.length) {
        const targetLevelId = sortedLevels[targetIndex].levelID;

        // Simple swap between current level and target level
        const levelMappings = [
            { old_level: levelId, new_level: targetLevelId },
            { old_level: targetLevelId, new_level: levelId }
        ];

        await applyLevelMappings(levelMappings);
    }
}

// Perform quick swap
async function performQuickSwap() {
    const level1 = document.getElementById('swapLevel1').value;
    const level2 = document.getElementById('swapLevel2').value;

    if (!level1 || !level2) {
        showNotification('Please select both levels to swap', 'error');
        return;
    }

    if (level1 === level2) {
        showNotification('Cannot swap a level with itself', 'error');
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}level_management_api.php?action=swap_levels`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                level1: parseInt(level1),
                level2: parseInt(level2)
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            closeModal('rearrangeLevelsModal');
            // Refresh the levels display
            await loadGameContent();
            displayGameLevels();
        } else {
            showNotification('Failed to swap levels: ' + (result.error || 'Unknown error'), 'error');
        }
    } catch (error) {
        console.error('Error swapping levels:', error);
        showNotification('Error swapping levels', 'error');
    }
}

// Apply level mappings to the database
async function applyLevelMappings(levelMappings) {
    if (levelMappings.length === 0) {
        showNotification('No changes detected in level order', 'info');
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}level_management_api.php?action=rearrange_levels`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                level_mappings: levelMappings
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            // Refresh the levels display
            await loadGameContent();
            displayGameLevels();
            populateInsertLevels();
            populateMoveQuestionsDropdowns();
        } else {
            showNotification('Failed to rearrange levels: ' + (result.error || 'Unknown error'), 'error');
        }
    } catch (error) {
        console.error('Error rearranging levels:', error);
        showNotification('Error rearranging levels', 'error');
    }
}

// Move Questions Between Levels Functions

// Show move questions modal
function showMoveQuestionsModal() {
    const sourceLevel = parseInt(document.getElementById('sourceQuestionLevel').value);
    const targetLevel = parseInt(document.getElementById('targetQuestionLevel').value);

    if (!sourceLevel || !targetLevel) {
        showNotification('Please select both source and target levels', 'error');
        return;
    }

    if (sourceLevel === targetLevel) {
        showNotification('Source and target levels cannot be the same', 'error');
        return;
    }

    // Find the levels
    const sourceLevelData = gameLevels.find(l => l.levelID === sourceLevel);
    const targetLevelData = gameLevels.find(l => l.levelID === targetLevel);

    if (!sourceLevelData || !targetLevelData) {
        showNotification('Selected levels not found', 'error');
        return;
    }

    if (sourceLevelData.questions.length === 0) {
        showNotification('Source level has no questions to move', 'error');
        return;
    }

    // Update modal information
    document.getElementById('sourceLevelName').textContent = `Level ${sourceLevel}`;
    document.getElementById('sourceLevelQuestionCount').textContent = sourceLevelData.questions.length;
    document.getElementById('targetLevelName').textContent = `Level ${targetLevel}`;
    document.getElementById('targetLevelQuestionCount').textContent = targetLevelData.questions.length;

    // Populate questions list
    populateQuestionsForMove(sourceLevelData);

    // Show modal
    openModal('moveQuestionsModal');
}

// Populate questions list for moving
function populateQuestionsForMove(sourceLevel) {
    const container = document.getElementById('moveQuestionsContainer');
    container.innerHTML = '';

    sourceLevel.questions.forEach((question, index) => {
        const questionItem = document.createElement('div');
        questionItem.className = 'move-question-item';
        questionItem.dataset.questionId = question.id;

        const questionText = question.question.length > 100
            ? question.question.substring(0, 100) + '...'
            : question.question;

        questionItem.innerHTML = `
            <div class="question-checkbox">
                <input type="checkbox" id="question-${question.id}" onchange="updateSelectedCount()">
            </div>
            <div class="question-info">
                <div class="question-number">Q${index + 1}</div>
                <div class="question-text">${questionText}</div>
                <div class="question-type">${question.quizType || 'Multiple Choice'}</div>
            </div>
        `;

        container.appendChild(questionItem);
    });

    updateSelectedCount();
}

// Select all questions
function selectAllQuestions() {
    const checkboxes = document.querySelectorAll('#moveQuestionsContainer input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelectedCount();
}

// Deselect all questions
function deselectAllQuestions() {
    const checkboxes = document.querySelectorAll('#moveQuestionsContainer input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelectedCount();
}

// Update selected count
function updateSelectedCount() {
    const checkboxes = document.querySelectorAll('#moveQuestionsContainer input[type="checkbox"]');
    const selectedCount = Array.from(checkboxes).filter(cb => cb.checked).length;

    document.querySelector('.selected-count').textContent = `${selectedCount} questions selected`;

    const moveBtn = document.getElementById('moveQuestionsBtn');
    moveBtn.disabled = selectedCount === 0;
}

// Execute question move
async function executeQuestionMove() {
    const sourceLevel = parseInt(document.getElementById('sourceQuestionLevel').value);
    const targetLevel = parseInt(document.getElementById('targetQuestionLevel').value);

    const checkboxes = document.querySelectorAll('#moveQuestionsContainer input[type="checkbox"]:checked');
    const questionIds = Array.from(checkboxes).map(cb => {
        return parseInt(cb.id.replace('question-', ''));
    });

    if (questionIds.length === 0) {
        showNotification('No questions selected', 'error');
        return;
    }

    if (!confirm(`Move ${questionIds.length} question(s) from Level ${sourceLevel} to Level ${targetLevel}?`)) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}game_content_api.php?action=move_questions`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                question_ids: questionIds,
                target_level: targetLevel
            })
        });

        const result = await response.json();

        if (result.success) {
            showNotification(`Successfully moved ${questionIds.length} question(s) to Level ${targetLevel}`, 'success');
            closeModal('moveQuestionsModal');
            // Refresh the data
            await loadGameContent();
            displayGameLevels();
            populateMoveQuestionsDropdowns();
        } else {
            showNotification('Failed to move questions: ' + (result.error || 'Unknown error'), 'error');
        }
    } catch (error) {
        console.error('Error moving questions:', error);
        showNotification('Error moving questions', 'error');
    }
}

// Bulk Operations

// Reverse all levels
async function reverseAllLevels() {
    if (!confirm('Are you sure you want to reverse the order of all levels?')) {
        return;
    }

    const sortedLevels = [...gameLevels].sort((a, b) => a.levelID - b.levelID);
    const levelMappings = [];

    sortedLevels.forEach((level, index) => {
        const newPosition = sortedLevels.length - index;
        if (level.levelID !== newPosition) {
            levelMappings.push({
                old_level: level.levelID,
                new_level: newPosition
            });
        }
    });

    if (levelMappings.length > 0) {
        await applyLevelMappings(levelMappings);
    } else {
        showNotification('Levels are already in reverse order', 'info');
    }
}

// Sort levels by question count
async function sortLevelsByQuestions() {
    if (!confirm('Are you sure you want to sort levels by question count (ascending)?')) {
        return;
    }

    const sortedByQuestions = [...gameLevels].sort((a, b) => a.questions.length - b.questions.length);
    const levelMappings = [];

    sortedByQuestions.forEach((level, index) => {
        const newPosition = index + 1;
        if (level.levelID !== newPosition) {
            levelMappings.push({
                old_level: level.levelID,
                new_level: newPosition
            });
        }
    });

    if (levelMappings.length > 0) {
        await applyLevelMappings(levelMappings);
    } else {
        showNotification('Levels are already sorted by question count', 'info');
    }
}

// Compact levels (remove gaps in numbering)
async function compactLevels() {
    if (!confirm('Are you sure you want to compact levels and remove gaps in numbering?')) {
        return;
    }

    const sortedLevels = [...gameLevels].sort((a, b) => a.levelID - b.levelID);
    const levelMappings = [];

    sortedLevels.forEach((level, index) => {
        const newPosition = index + 1;
        if (level.levelID !== newPosition) {
            levelMappings.push({
                old_level: level.levelID,
                new_level: newPosition
            });
        }
    });

    if (levelMappings.length > 0) {
        await applyLevelMappings(levelMappings);
    } else {
        showNotification('Levels are already compacted', 'info');
    }
}
