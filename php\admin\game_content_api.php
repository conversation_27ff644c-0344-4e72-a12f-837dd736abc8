<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../dbconnection.php';

class GameContentAPI {
    private $conn;
    
    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }
    
    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $action = $_GET['action'] ?? '';
        
        try {
            switch ($method) {
                case 'GET':
                    $this->handleGet($action);
                    break;
                case 'POST':
                    $this->handlePost($action);
                    break;
                case 'PUT':
                    $this->handlePut($action);
                    break;
                case 'DELETE':
                    $this->handleDelete($action);
                    break;
                default:
                    $this->sendResponse(['error' => 'Method not allowed'], 405);
            }
        } catch (Exception $e) {
            $this->sendResponse(['error' => $e->getMessage()], 500);
        }
    }
    
    private function handleGet($action) {
        switch ($action) {
            case 'get_all_content':
                $this->getAllContent();
                break;
            case 'get_content_by_level':
                $level = $_GET['level'] ?? 0;
                $this->getContentByLevel($level);
                break;
            case 'get_levels':
                $this->getLevels();
                break;
            default:
                $this->sendResponse(['error' => 'Invalid action'], 400);
        }
    }
    
    private function handlePost($action) {
        // Handle both FormData and JSON input for POST requests
        $input = null;
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';

        if (strpos($contentType, 'application/json') !== false) {
            $input = json_decode(file_get_contents('php://input'), true);
        } else {
            // Handle FormData (multipart/form-data or application/x-www-form-urlencoded)
            $input = $_POST;
        }

        if ($input === null) {
            $this->sendResponse(['error' => 'Invalid input data'], 400);
            return;
        }

        switch ($action) {
            case 'add_content':
                $this->addContent($input);
                break;
            default:
                $this->sendResponse(['error' => 'Invalid action'], 400);
        }
    }
    
    private function handlePut($action) {
        $input = json_decode(file_get_contents('php://input'), true);

        if ($input === null) {
            $this->sendResponse(['error' => 'Invalid JSON input for PUT request'], 400);
            return;
        }

        switch ($action) {
            case 'update_content':
                $this->updateContent($input);
                break;
            case 'move_questions':
                $this->moveQuestions($input);
                break;
            default:
                $this->sendResponse(['error' => 'Invalid action'], 400);
        }
    }
    
    private function handleDelete($action) {
        switch ($action) {
            case 'delete_content':
                $contentId = $_GET['content_id'] ?? 0;
                $this->deleteContent($contentId);
                break;
            default:
                $this->sendResponse(['error' => 'Invalid action'], 400);
        }
    }
    
    private function getAllContent() {
        $query = "SELECT *, COALESCE(can_edit, 1) as can_edit, COALESCE(quiz_type, 'Multiple Choice') as quiz_type FROM game_content ORDER BY level_number, content_id";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        $content = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $this->sendResponse(['success' => true, 'data' => $content]);
    }
    
    private function getContentByLevel($level) {
        $query = "SELECT *, COALESCE(can_edit, 1) as can_edit, COALESCE(quiz_type, 'Multiple Choice') as quiz_type FROM game_content WHERE level_number = ? ORDER BY content_id";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$level]);

        $content = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $this->sendResponse(['success' => true, 'data' => $content]);
    }
    
    private function getLevels() {
        $query = "SELECT DISTINCT level_number, COUNT(*) as question_count,
                  MIN(COALESCE(can_edit, 1)) as can_edit
                  FROM game_content
                  GROUP BY level_number
                  ORDER BY level_number";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        $levels = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $this->sendResponse(['success' => true, 'data' => $levels]);
    }
    
    private function addContent($data) {
        $quizType = isset($data['quiz_type']) ? $data['quiz_type'] : 'Multiple Choice';
        $query = "INSERT INTO game_content (level_number, question_text, option1, option2, option3, option4, correct_answer, quiz_type, can_edit) 
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)";
        
        $stmt = $this->conn->prepare($query);
        $result = $stmt->execute([
            $data['level_number'],
            $data['question_text'],
            $data['option1'],
            $data['option2'],
            $data['option3'],
            $data['option4'],
            $data['correct_answer'],
            $quizType
        ]);
        
        if ($result) {
            $contentId = $this->conn->lastInsertId();
            $this->sendResponse(['success' => true, 'content_id' => $contentId, 'message' => 'Content added successfully']);
        } else {
            $this->sendResponse(['success' => false, 'error' => 'Failed to add content'], 500);
        }
    }
    
    private function updateContent($data) {
        // Debug: Log received data
        error_log('Update content received data: ' . json_encode($data));

        // Validate required fields
        $requiredFields = ['content_id', 'level_number', 'question_text', 'correct_answer'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                $this->sendResponse(['success' => false, 'error' => "Missing required field: $field"], 400);
                return;
            }
        }

        $quizType = isset($data['quiz_type']) ? $data['quiz_type'] : 'Multiple Choice';

        // For Multiple Choice, validate options are present
        if ($quizType === 'Multiple Choice') {
            $optionFields = ['option1', 'option2', 'option3', 'option4'];
            foreach ($optionFields as $field) {
                if (!isset($data[$field])) {
                    $this->sendResponse(['success' => false, 'error' => "Missing required field for Multiple Choice: $field"], 400);
                    return;
                }
            }
        }

        // Set default empty values for options if not provided (for Matching Type)
        $option1 = isset($data['option1']) ? $data['option1'] : '';
        $option2 = isset($data['option2']) ? $data['option2'] : '';
        $option3 = isset($data['option3']) ? $data['option3'] : '';
        $option4 = isset($data['option4']) ? $data['option4'] : '';
        // Check if the content exists and can be edited
        $checkQuery = "SELECT can_edit FROM game_content WHERE content_id = ?";
        $checkStmt = $this->conn->prepare($checkQuery);
        $checkStmt->execute([$data['content_id']]);
        $contentInfo = $checkStmt->fetch(PDO::FETCH_ASSOC);

        if (!$contentInfo) {
            $this->sendResponse(['success' => false, 'error' => 'Content not found'], 404);
            return;
        }

        if ($contentInfo['can_edit'] != 1) {
            $this->sendResponse(['success' => false, 'error' => 'This content cannot be edited'], 403);
            return;
        }

        $query = "UPDATE game_content
                  SET level_number = ?, question_text = ?, option1 = ?, option2 = ?, option3 = ?, option4 = ?, correct_answer = ?, quiz_type = ?
                  WHERE content_id = ?";

        try {
            $stmt = $this->conn->prepare($query);
            $result = $stmt->execute([
                $data['level_number'],
                $data['question_text'],
                $option1,
                $option2,
                $option3,
                $option4,
                $data['correct_answer'],
                $quizType,
                $data['content_id']
            ]);

            if ($result) {
                $rowsAffected = $stmt->rowCount();
                if ($rowsAffected > 0) {
                    $this->sendResponse(['success' => true, 'message' => 'Content updated successfully']);
                } else {
                    $this->sendResponse(['success' => false, 'error' => 'No content found with the specified ID'], 404);
                }
            } else {
                $this->sendResponse(['success' => false, 'error' => 'Failed to execute update query'], 500);
            }
        } catch (PDOException $e) {
            $this->sendResponse(['success' => false, 'error' => 'Database error: ' . $e->getMessage()], 500);
        }
    }
    
    private function deleteContent($contentId) {
        $query = "DELETE FROM game_content WHERE content_id = ?";
        $stmt = $this->conn->prepare($query);
        $result = $stmt->execute([$contentId]);
        
        if ($result) {
            $this->sendResponse(['success' => true, 'message' => 'Content deleted successfully']);
        } else {
            $this->sendResponse(['success' => false, 'error' => 'Failed to delete content'], 500);
        }
    }

    private function moveQuestions($data) {
        // Validate required fields
        if (!isset($data['question_ids']) || !isset($data['target_level'])) {
            $this->sendResponse(['error' => 'Missing required fields: question_ids and target_level'], 400);
            return;
        }

        $questionIds = $data['question_ids'];
        $targetLevel = intval($data['target_level']);

        // Validate question IDs array
        if (!is_array($questionIds) || empty($questionIds)) {
            $this->sendResponse(['error' => 'question_ids must be a non-empty array'], 400);
            return;
        }

        // Validate target level exists
        $checkLevelQuery = "SELECT COUNT(*) as count FROM game_content WHERE level_number = ?";
        $checkStmt = $this->conn->prepare($checkLevelQuery);
        $checkStmt->execute([$targetLevel]);
        $levelExists = $checkStmt->fetch(PDO::FETCH_ASSOC)['count'] > 0;

        if (!$levelExists) {
            $this->sendResponse(['error' => 'Target level does not exist'], 400);
            return;
        }

        try {
            // Start transaction
            $this->conn->beginTransaction();

            $successCount = 0;
            $errors = [];

            foreach ($questionIds as $questionId) {
                $questionId = intval($questionId);

                // Check if question exists and can be edited
                $checkQuery = "SELECT content_id, level_number, can_edit FROM game_content WHERE content_id = ?";
                $checkStmt = $this->conn->prepare($checkQuery);
                $checkStmt->execute([$questionId]);
                $question = $checkStmt->fetch(PDO::FETCH_ASSOC);

                if (!$question) {
                    $errors[] = "Question ID $questionId not found";
                    continue;
                }

                if ($question['can_edit'] != 1) {
                    $errors[] = "Question ID $questionId cannot be edited";
                    continue;
                }

                if ($question['level_number'] == $targetLevel) {
                    $errors[] = "Question ID $questionId is already in target level";
                    continue;
                }

                // Update the question's level
                $updateQuery = "UPDATE game_content SET level_number = ? WHERE content_id = ?";
                $updateStmt = $this->conn->prepare($updateQuery);

                if ($updateStmt->execute([$targetLevel, $questionId])) {
                    $successCount++;
                } else {
                    $errors[] = "Failed to move question ID $questionId";
                }
            }

            // Commit transaction
            $this->conn->commit();

            if ($successCount > 0 && empty($errors)) {
                $this->sendResponse([
                    'success' => true,
                    'message' => "Successfully moved $successCount question(s) to level $targetLevel",
                    'moved_count' => $successCount
                ]);
            } else if ($successCount > 0 && !empty($errors)) {
                $this->sendResponse([
                    'success' => true,
                    'message' => "Moved $successCount question(s) to level $targetLevel, but some failed",
                    'moved_count' => $successCount,
                    'errors' => $errors
                ]);
            } else {
                $this->sendResponse([
                    'success' => false,
                    'error' => 'No questions were moved',
                    'errors' => $errors
                ], 400);
            }

        } catch (Exception $e) {
            // Rollback transaction
            $this->conn->rollBack();
            $this->sendResponse(['error' => 'Failed to move questions: ' . $e->getMessage()], 500);
        }
    }

    private function sendResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        echo json_encode($data);
        exit;
    }
}

// Initialize and handle the request
$api = new GameContentAPI();
$api->handleRequest();
